#!/usr/bin/env python3
"""
MindsDB MCP API Test Script
===========================

This script tests the MindsDB MCP (Model Context Protocol) API
and demonstrates proper authentication and usage.
"""

import requests
import json
import sys
from typing import Dict, Any

# MCP API Configuration
MCP_BASE_URL = "http://127.0.0.1:47337"
MCP_ACCESS_TOKEN = "mcp_token_abc123"

def test_mcp_connection() -> bool:
    """Test basic MCP API connection with authentication"""
    try:
        headers = {
            "Authorization": f"Bearer {MCP_ACCESS_TOKEN}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(
            f"{MCP_BASE_URL}/",
            headers=headers,
            timeout=10
        )
        
        print(f"🔗 MCP API Response Status: {response.status_code}")
        print(f"📝 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ MCP API connection successful!")
            return True
        elif response.status_code == 401:
            print("❌ MCP API authentication failed (401 Unauthorized)")
            print("🔧 Check your MCP_ACCESS_TOKEN configuration")
            return False
        else:
            print(f"⚠️  MCP API returned status code: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ MCP API connection failed: {str(e)}")
        return False

def test_mcp_tools() -> bool:
    """Test MCP tools functionality"""
    try:
        headers = {
            "Authorization": f"Bearer {MCP_ACCESS_TOKEN}",
            "Content-Type": "application/json"
        }
        
        # Test list_databases tool
        print("\n🔍 Testing MCP list_databases tool...")
        
        payload = {
            "method": "tools/call",
            "params": {
                "name": "list_databases",
                "arguments": {}
            }
        }
        
        response = requests.post(
            f"{MCP_BASE_URL}/",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"📊 List databases response: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Databases found: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"❌ List databases failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ MCP tools test failed: {str(e)}")
        return False

def test_mcp_query() -> bool:
    """Test MCP query functionality"""
    try:
        headers = {
            "Authorization": f"Bearer {MCP_ACCESS_TOKEN}",
            "Content-Type": "application/json"
        }
        
        # Test query tool
        print("\n🔍 Testing MCP query tool...")
        
        payload = {
            "method": "tools/call",
            "params": {
                "name": "query",
                "arguments": {
                    "query": "SHOW DATABASES;"
                }
            }
        }
        
        response = requests.post(
            f"{MCP_BASE_URL}/",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"📊 Query response: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Query result: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"❌ Query failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ MCP query test failed: {str(e)}")
        return False

def test_mcp_websocket() -> bool:
    """Test if MCP WebSocket endpoint is available"""
    try:
        # Check if WebSocket endpoint exists
        headers = {
            "Authorization": f"Bearer {MCP_ACCESS_TOKEN}",
            "Upgrade": "websocket",
            "Connection": "Upgrade"
        }
        
        response = requests.get(
            f"{MCP_BASE_URL}/ws",
            headers=headers,
            timeout=10
        )
        
        print(f"\n🔌 WebSocket endpoint test: {response.status_code}")
        if response.status_code in [101, 426]:  # WebSocket upgrade codes
            print("✅ WebSocket endpoint available")
            return True
        else:
            print(f"⚠️  WebSocket endpoint response: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ WebSocket test failed: {str(e)}")
        return False

def check_mindsdb_status() -> bool:
    """Check if MindsDB main API is running"""
    try:
        response = requests.get("http://127.0.0.1:47334/", timeout=10)
        print(f"🌐 MindsDB HTTP API Status: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ MindsDB HTTP API check failed: {str(e)}")
        return False

def print_mcp_info():
    """Print MCP API information and usage"""
    print("\n" + "=" * 60)
    print("📋 MCP API Information")
    print("=" * 60)
    
    print(f"\n🔗 **MCP API Endpoint**: {MCP_BASE_URL}")
    print(f"🔑 **Access Token**: {MCP_ACCESS_TOKEN}")
    print(f"🔐 **Authentication**: Bearer token required")
    
    print("\n🛠️  **Available MCP Tools**:")
    print("1. **list_databases** - List all connected data sources")
    print("2. **query** - Execute SQL queries on federated data")
    
    print("\n📝 **Example Usage**:")
    print("```bash")
    print("curl -X POST http://127.0.0.1:47337/ \\")
    print("  -H 'Authorization: Bearer mcp_token_abc123' \\")
    print("  -H 'Content-Type: application/json' \\")
    print("  -d '{")
    print('    "method": "tools/call",')
    print('    "params": {')
    print('      "name": "list_databases",')
    print('      "arguments": {}')
    print('    }')
    print("  }'")
    print("```")
    
    print("\n🎯 **For Respond Chat Interface**:")
    print("- The MCP API enables the Respond interface")
    print("- Authentication must be properly configured")
    print("- Look for robot icon (🤖) in MindsDB web UI")
    print("- Login with: mindsdb / mindsdb123")

def main():
    """Main execution"""
    print("🚀 MindsDB MCP API Test")
    print("=" * 50)
    
    # Check MindsDB main API
    print("🔍 Checking MindsDB HTTP API...")
    if not check_mindsdb_status():
        print("❌ MindsDB HTTP API is not accessible")
        print("💡 Make sure MindsDB is running on port 47334")
        return False
    
    # Test MCP connection
    print("\n🔍 Testing MCP API connection...")
    if not test_mcp_connection():
        print("❌ MCP API connection failed")
        print("💡 Check if MCP API is enabled and token is correct")
        return False
    
    # Test MCP tools
    if test_mcp_tools():
        print("✅ MCP tools are working!")
    
    # Test MCP query
    if test_mcp_query():
        print("✅ MCP query functionality is working!")
    
    # Test WebSocket
    test_mcp_websocket()
    
    # Print information
    print_mcp_info()
    
    print("\n" + "=" * 60)
    print("🎉 MCP API Test Complete!")
    print("=" * 60)
    
    print("\n✅ **Next Steps**:")
    print("1. Login to MindsDB web UI: http://127.0.0.1:47334/")
    print("2. Use credentials: mindsdb / mindsdb123")
    print("3. Look for the Respond/Chat interface")
    print("4. Start chatting with your data!")
    
    return True

if __name__ == "__main__":
    main()
