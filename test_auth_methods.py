#!/usr/bin/env python3
"""
Test Different Authentication Methods for MindsDB
=================================================

This script tests various authentication methods to access MindsDB APIs.
"""

import requests
import json
import base64

MINDSDB_BASE_URL = "http://localhost:47334"

def test_no_auth():
    """Test without authentication"""
    print("🔍 Testing without authentication...")
    try:
        response = requests.get(f"{MINDSDB_BASE_URL}/", timeout=10)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ No authentication required")
            return True
        elif response.status_code == 401:
            print("🔐 Authentication required")
            return False
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_basic_auth():
    """Test with basic authentication"""
    print("\n🔍 Testing basic authentication...")
    try:
        response = requests.get(
            f"{MINDSDB_BASE_URL}/",
            auth=("mindsdb", "mindsdb123"),
            timeout=10
        )
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Basic auth works")
            return True
        else:
            print(f"❌ Basic auth failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_header_auth():
    """Test with authorization header"""
    print("\n🔍 Testing authorization header...")
    try:
        # Create basic auth header manually
        credentials = base64.b64encode(b"mindsdb:mindsdb123").decode('ascii')
        headers = {
            "Authorization": f"Basic {credentials}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(
            f"{MINDSDB_BASE_URL}/",
            headers=headers,
            timeout=10
        )
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Header auth works")
            return True
        else:
            print(f"❌ Header auth failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_sql_api_auth():
    """Test SQL API with different auth methods"""
    print("\n🔍 Testing SQL API authentication...")
    
    sql_url = f"{MINDSDB_BASE_URL}/api/sql/query"
    query = {"query": "SHOW DATABASES;"}
    
    # Test 1: No auth
    print("  📋 SQL API - No auth...")
    try:
        response = requests.post(sql_url, json=query, timeout=10)
        print(f"    Status: {response.status_code}")
        if response.status_code == 200:
            print("    ✅ SQL API works without auth")
            return True
    except Exception as e:
        print(f"    ❌ Error: {e}")
    
    # Test 2: Basic auth
    print("  📋 SQL API - Basic auth...")
    try:
        response = requests.post(
            sql_url, 
            json=query, 
            auth=("mindsdb", "mindsdb123"),
            timeout=10
        )
        print(f"    Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("    ✅ SQL API works with basic auth")
            print(f"    📊 Result: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"    ❌ SQL API basic auth failed: {response.text}")
    except Exception as e:
        print(f"    ❌ Error: {e}")
    
    # Test 3: Header auth
    print("  📋 SQL API - Header auth...")
    try:
        credentials = base64.b64encode(b"mindsdb:mindsdb123").decode('ascii')
        headers = {
            "Authorization": f"Basic {credentials}",
            "Content-Type": "application/json"
        }
        response = requests.post(sql_url, json=query, headers=headers, timeout=10)
        print(f"    Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("    ✅ SQL API works with header auth")
            print(f"    📊 Result: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"    ❌ SQL API header auth failed: {response.text}")
    except Exception as e:
        print(f"    ❌ Error: {e}")
    
    return False

def test_login_endpoint():
    """Test if there's a login endpoint"""
    print("\n🔍 Testing login endpoint...")
    
    login_endpoints = [
        "/api/auth/login",
        "/api/login",
        "/login",
        "/auth/login"
    ]
    
    for endpoint in login_endpoints:
        try:
            url = f"{MINDSDB_BASE_URL}{endpoint}"
            print(f"  📋 Trying: {url}")
            
            # Try GET first
            response = requests.get(url, timeout=5)
            print(f"    GET Status: {response.status_code}")
            
            # Try POST with credentials
            login_data = {
                "username": "mindsdb",
                "password": "mindsdb123"
            }
            response = requests.post(url, json=login_data, timeout=5)
            print(f"    POST Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"    ✅ Login endpoint found: {endpoint}")
                print(f"    📊 Response: {response.text[:200]}")
                return True
                
        except Exception as e:
            print(f"    ❌ Error: {e}")
    
    print("  ⚠️  No login endpoint found")
    return False

def test_container_direct():
    """Test accessing MindsDB directly inside the container"""
    print("\n🔍 Testing container direct access...")
    
    try:
        # Execute curl inside the container
        import subprocess
        
        # Test 1: Direct access
        result = subprocess.run([
            "docker", "exec", "mindsdb_chat", 
            "curl", "-s", "http://localhost:47334/"
        ], capture_output=True, text=True, timeout=10)
        
        print(f"  📋 Container direct access:")
        print(f"    Return code: {result.returncode}")
        print(f"    Output: {result.stdout[:200]}")
        
        if result.returncode == 0 and "MindsDB" in result.stdout:
            print("    ✅ Container direct access works")
            return True
        
        # Test 2: SQL API inside container
        result = subprocess.run([
            "docker", "exec", "mindsdb_chat", 
            "curl", "-s", "-X", "POST",
            "-H", "Content-Type: application/json",
            "-d", '{"query": "SHOW DATABASES;"}',
            "http://localhost:47334/api/sql/query"
        ], capture_output=True, text=True, timeout=10)
        
        print(f"  📋 Container SQL API:")
        print(f"    Return code: {result.returncode}")
        print(f"    Output: {result.stdout}")
        
        if result.returncode == 0:
            print("    ✅ Container SQL API accessible")
            return True
            
    except Exception as e:
        print(f"    ❌ Error: {e}")
    
    return False

def main():
    """Main execution"""
    print("🚀 MindsDB Authentication Testing")
    print("=" * 50)
    
    # Test different authentication methods
    methods = [
        ("No Authentication", test_no_auth),
        ("Basic Authentication", test_basic_auth),
        ("Header Authentication", test_header_auth),
        ("SQL API Authentication", test_sql_api_auth),
        ("Login Endpoint", test_login_endpoint),
        ("Container Direct Access", test_container_direct)
    ]
    
    results = {}
    for name, test_func in methods:
        print(f"\n{'='*20} {name} {'='*20}")
        try:
            results[name] = test_func()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results[name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Authentication Test Results")
    print("=" * 60)
    
    for name, success in results.items():
        status = "✅" if success else "❌"
        print(f"{status} {name}")
    
    # Recommendations
    print("\n💡 Recommendations:")
    if results.get("Container Direct Access"):
        print("- Container direct access works - authentication might be external only")
    if results.get("SQL API Authentication"):
        print("- SQL API authentication successful - use this method")
    if not any(results.values()):
        print("- All authentication methods failed - check container configuration")
        print("- Try restarting container with different auth settings")
    
    return any(results.values())

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
