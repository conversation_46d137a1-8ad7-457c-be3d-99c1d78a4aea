{"version": "1.0", "description": "MindsDB Configuration with Vertex AI as Default AI Provider", "default_llm": {"provider": "vertex", "model_name": "gemini-2.0-flash-exp", "project_id": "your-project-id", "location": "us-central1", "temperature": 0.7, "max_tokens": 8192, "top_p": 0.95, "top_k": 40}, "default_embedding_model": {"provider": "vertex", "model_name": "text-embedding-004", "project_id": "your-project-id", "location": "us-central1", "task_type": "RETRIEVAL_DOCUMENT", "dimensionality": 768}, "default_reranking_model": {"provider": "vertex", "model_name": "gemini-2.0-flash-exp", "project_id": "your-project-id", "location": "us-central1", "temperature": 0.3, "max_tokens": 4096, "method": "multi-class"}, "api": {"http": {"host": "127.0.0.1", "port": "47334", "restart_on_failure": true, "max_restart_count": 3, "max_restart_interval_seconds": 60, "server": {"type": "waitress", "config": {"threads": 16, "max_request_body_size": 10737418240, "inbuf_overflow": 10737418240}}}, "mysql": {"host": "127.0.0.1", "port": "47335", "database": "mindsdb", "ssl": true, "restart_on_failure": true, "max_restart_count": 3, "max_restart_interval_seconds": 60}, "mongodb": {"host": "127.0.0.1", "port": "47336", "restart_on_failure": true, "max_restart_count": 3, "max_restart_interval_seconds": 60}, "postgres": {"host": "127.0.0.1", "port": "55432", "restart_on_failure": true, "max_restart_count": 3, "max_restart_interval_seconds": 60}, "mcp": {"host": "127.0.0.1", "port": "47337", "restart_on_failure": true, "max_restart_count": 3, "max_restart_interval_seconds": 60}}, "integrations": {"vertex": {"enabled": true, "default_project_id": "your-project-id", "default_location": "us-central1", "authentication_method": "application_default_credentials"}}, "auth": {"http_auth_enabled": false}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "handlers": {"console": {"enabled": true, "level": "INFO"}, "file": {"enabled": true, "level": "DEBUG", "filename": "mindsdb.log", "max_size": "10MB", "backup_count": 5}}}, "cache": {"type": "local"}, "ml_task_queue": {"type": "local"}, "jobs": {"disable": false}, "tasks": {"disable": false}, "default_project": "mindsdb", "gui": {"autoupdate": true}, "debug": false, "environment": "local", "cloud": false, "data_catalog": {"enabled": false}, "a2a": {"host": "localhost", "port": 47338, "mindsdb_host": "localhost", "mindsdb_port": 47334, "agent_name": "my_agent", "project_name": "mindsdb", "enabled": false}}