# MindsDB + Gemini 2.5 Integration Guide

## 🚀 Overview

This integration brings Google's latest **Gemini 2.5 Flash** and **Gemini 2.5 Pro** models to MindsDB, offering cutting-edge AI capabilities with massive context windows, advanced reasoning, and enhanced performance.

## 🆕 What's New in Gemini 2.5

### Gemini 2.5 Flash Preview
- **Massive Context Window**: 1 million tokens (2 million coming soon)
- **Thinking Models**: Enhanced reasoning with visible thought processes
- **Balanced Performance**: Optimized for speed and cost-effectiveness
- **Multimodal Input**: Text, images, audio, and video support
- **Enhanced Efficiency**: Better price-to-performance ratio

### Gemini 2.5 Pro Preview
- **Advanced Reasoning**: State-of-the-art thinking model capabilities
- **Largest Context**: Up to 2 million token context window
- **Complex Problem Solving**: Multi-step reasoning and analysis
- **Document Analysis**: Process 400+ page documents without chunking
- **Code Generation**: Superior markdown and structured data extraction

## 🔧 Configuration

### Default Models Configuration

The integration is configured with:

```json
{
  "default_llm": {
    "provider": "vertex",
    "model_name": "gemini-2.5-flash-preview",
    "context_window": 1000000,
    "max_tokens": 32768
  },
  "default_reranking_model": {
    "provider": "vertex", 
    "model_name": "gemini-2.5-pro-preview",
    "context_window": 2000000,
    "max_tokens": 8192
  }
}
```

### Environment Variables

```bash
# Gemini 2.5 Flash for general LLM tasks
export MINDSDB_DEFAULT_LLM_MODEL='gemini-2.5-flash-preview'

# Gemini 2.5 Pro for advanced reasoning and reranking
export MINDSDB_DEFAULT_RERANKING_MODEL_NAME='gemini-2.5-pro-preview'
```

## 🎯 Key Use Cases

### 1. Large Document Analysis
Process entire documents without chunking limitations:

```sql
CREATE ML_ENGINE document_analyzer
FROM vertex
USING
    model_name = 'gemini-2.5-flash-preview',
    context_window = 1000000;

CREATE MODEL contract_analyzer
PREDICT analysis
USING
    engine = 'document_analyzer',
    prompt_template = 'Analyze this 400-page legal contract and extract key terms, obligations, and risks: {{document_text}}';
```

### 2. Advanced Reasoning Tasks
Leverage thinking capabilities for complex problem solving:

```sql
CREATE ML_ENGINE reasoning_engine
FROM vertex
USING
    model_name = 'gemini-2.5-pro-preview',
    temperature = 0.3,
    context_window = 2000000;

CREATE MODEL strategic_advisor
PREDICT recommendation
USING
    engine = 'reasoning_engine',
    prompt_template = 'Think step-by-step about this business challenge and provide a comprehensive strategy: {{challenge}}';
```

### 3. Extended Conversational Memory
Maintain context over long conversations:

```sql
CREATE MODEL conversation_ai
PREDICT response
USING
    engine = 'gemini_25_flash_engine',
    prompt_template = 'Continue this conversation with full context awareness: {{conversation_history}}';
```

### 4. Code and Markdown Generation
Enhanced structured output generation:

```sql
CREATE MODEL code_generator
PREDICT code
USING
    engine = 'gemini_25_pro_engine',
    prompt_template = 'Generate production-ready code with documentation: {{requirements}}';
```

## 🔍 Advanced Features

### Thinking Process Visibility
Gemini 2.5 models can show their reasoning process:

```sql
SELECT 
    thinking_process,
    final_answer
FROM reasoning_model
WHERE input = 'Solve this complex optimization problem step by step';
```

### Massive Context Processing
Handle large datasets and documents:

```sql
-- Process multiple documents simultaneously
SELECT analysis 
FROM document_analyzer
WHERE input = CONCAT(
    'Document 1: ', doc1_text,
    'Document 2: ', doc2_text,
    'Document 3: ', doc3_text,
    'Provide comparative analysis across all documents'
);
```

### Long-Range Pattern Detection
Identify trends across extensive data:

```sql
CREATE MODEL pattern_detector
PREDICT insights
USING
    engine = 'gemini_25_flash_engine',
    prompt_template = 'Analyze these time series data points and identify long-term patterns: {{data_points}}';
```

## 🚀 Getting Started

### 1. Restart MindsDB with New Configuration
```bash
# Stop current MindsDB instance
# Update configuration (already done)
# Restart with new Gemini 2.5 models
source setup_mindsdb_env.sh && python -m mindsdb --config config.json
```

### 2. Test the Integration
```bash
python test_gemini_25_integration.py
```

### 3. Create Your First Gemini 2.5 Model
```sql
-- Create a Gemini 2.5 Flash engine
CREATE ML_ENGINE my_gemini_25_engine
FROM vertex
USING
    model_name = 'gemini-2.5-flash-preview',
    project_id = 'your-project-id',
    location = 'us-central1',
    temperature = 0.7,
    max_tokens = 32768,
    context_window = 1000000;

-- Create a model using the engine
CREATE MODEL my_ai_assistant
PREDICT response
USING
    engine = 'my_gemini_25_engine',
    prompt_template = 'You are an advanced AI assistant. {{user_input}}';

-- Test the model
SELECT response 
FROM my_ai_assistant 
WHERE user_input = 'Explain quantum computing in simple terms';
```

## 📊 Performance Benefits

### Context Window Comparison
| Model | Context Window | Use Case |
|-------|----------------|----------|
| Gemini 1.5 Pro | 128K tokens | Standard documents |
| Gemini 2.0 Flash | 1M tokens | Large documents |
| **Gemini 2.5 Flash** | **1M tokens** | **Enhanced efficiency** |
| **Gemini 2.5 Pro** | **2M tokens** | **Massive documents** |

### Key Improvements
- **🚀 Speed**: Faster response times with Flash variant
- **💰 Cost**: Better price-to-performance ratio
- **🧠 Reasoning**: Enhanced multi-step problem solving
- **📄 Documents**: Process 400+ page PDFs without chunking
- **💬 Memory**: Extended conversational context
- **🔍 Patterns**: Long-range pattern recognition

## 🛠️ Troubleshooting

### Common Issues

1. **Model Not Available**
   ```
   Error: Model 'gemini-2.5-flash-preview' not found
   ```
   **Solution**: Ensure you have access to Gemini 2.5 preview models in your GCP project.

2. **Context Window Exceeded**
   ```
   Error: Input exceeds maximum context window
   ```
   **Solution**: Use Gemini 2.5 Pro for larger contexts (2M tokens).

3. **Authentication Issues**
   ```
   Error: Permission denied for Vertex AI
   ```
   **Solution**: Verify Application Default Credentials and project permissions.

### Verification Commands
```bash
# Check model availability
gcloud ai models list --region=us-central1 --filter="displayName:gemini-2.5"

# Verify authentication
gcloud auth application-default print-access-token

# Test API access
curl -H "Authorization: Bearer $(gcloud auth application-default print-access-token)" \
     "https://us-central1-aiplatform.googleapis.com/v1/projects/YOUR_PROJECT/locations/us-central1/models"
```

## 🎉 Next Steps

1. **Explore Advanced Features**: Try thinking models and large context processing
2. **Optimize for Your Use Case**: Adjust temperature and token limits
3. **Scale Your Implementation**: Deploy models for production workloads
4. **Monitor Performance**: Track usage and optimize costs

## 📚 Resources

- [MindsDB Documentation](https://docs.mindsdb.com/)
- [Vertex AI Gemini Models](https://cloud.google.com/vertex-ai/generative-ai/docs/models)
- [Gemini 2.5 Blog Post](https://mindsdb.com/blog/minds-now-supports-gemini-2-5-flash-and-gemini-2-5-pro)
- [Google AI Gemini API](https://ai.google.dev/gemini-api/docs/models)

---

**🚀 Ready to leverage the power of Gemini 2.5 with MindsDB!**
