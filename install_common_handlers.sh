#!/bin/bash

# Install Common MindsDB Handler Dependencies
# This script installs dependencies for commonly used MindsDB handlers

echo "🔧 Installing common MindsDB handler dependencies..."

# Activate virtual environment
source venv/bin/activate

# Core handlers (already installed)
echo "✅ Core handlers already installed"

# AI/ML handlers
echo "📦 Installing AI/ML handler dependencies..."
pip install '.[openai]' --quiet
pip install '.[anthropic]' --quiet
pip install '.[huggingface]' --quiet
pip install '.[langchain]' --quiet

# Database handlers
echo "📦 Installing database handler dependencies..."
pip install '.[postgres]' --quiet
pip install '.[mysql]' --quiet
pip install '.[mongodb]' --quiet
pip install '.[sqlite]' --quiet

# API handlers
echo "📦 Installing API handler dependencies..."
pip install '.[github]' --quiet
pip install '.[slack]' --quiet
pip install '.[twitter]' --quiet

# File/Data handlers
echo "📦 Installing file/data handler dependencies..."
pip install '.[file]' --quiet
pip install '.[web]' --quiet
pip install '.[email]' --quiet

# Vector database handlers
echo "📦 Installing vector database handler dependencies..."
pip install '.[chromadb]' --quiet
pip install '.[pinecone]' --quiet
pip install '.[weaviate]' --quiet

# Cloud handlers
echo "📦 Installing cloud handler dependencies..."
pip install '.[s3]' --quiet
pip install '.[bigquery]' --quiet

# Install individual packages that might be missing
echo "📦 Installing additional dependencies..."

# GitHub handler
pip install pygithub==2.6.1 --quiet

# Common ML libraries
pip install scikit-learn --quiet
pip install numpy --quiet
pip install pandas --quiet

# Common API libraries
pip install requests --quiet
pip install httpx --quiet

# Common utility libraries
pip install python-dotenv --quiet
pip install pyyaml --quiet

echo "✅ Common handler dependencies installed successfully!"
echo ""
echo "📋 Installed handler categories:"
echo "- ✅ AI/ML: OpenAI, Anthropic, Hugging Face, LangChain"
echo "- ✅ Databases: PostgreSQL, MySQL, MongoDB, SQLite"
echo "- ✅ APIs: GitHub, Slack, Twitter"
echo "- ✅ Files: File, Web, Email"
echo "- ✅ Vector DBs: ChromaDB, Pinecone, Weaviate"
echo "- ✅ Cloud: S3, BigQuery"
echo ""
echo "🚀 MindsDB should now have fewer handler import errors!"
