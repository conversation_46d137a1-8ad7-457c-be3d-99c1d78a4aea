#!/bin/bash

# MindsDB Environment Configuration Script with Vertex AI Integration
# This script sets up all necessary environment variables for MindsDB with Vertex AI as default

echo "Setting up MindsDB environment variables with Vertex AI integration..."

# ============================================================================
# CORE MINDSDB CONFIGURATION
# ============================================================================

# MindsDB APIs to start (including MCP)
export MINDSDB_APIS='http,mysql,mongodb,postgres,mcp'

# MindsDB Storage Directory (optional - uses default if not set)
# export MINDSDB_STORAGE_DIR="$HOME/mindsdb_data"

# MindsDB Database Connection (optional - uses SQLite by default)
# export MINDSDB_DB_CON='sqlite:///mindsdb.db'

# MindsDB Default Project Name
export MINDSDB_DEFAULT_PROJECT='mindsdb'

# MindsDB Log Level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
export MINDSDB_LOG_LEVEL='INFO'

# MindsDB Server Type (waitress, flask, gunicorn)
export MINDSDB_DEFAULT_SERVER='waitress'

# ============================================================================
# GOOGLE CLOUD / VERTEX AI CONFIGURATION
# ============================================================================

# Google Cloud Project ID (REQUIRED - replace with your project ID)
export GOOGLE_CLOUD_PROJECT='your-project-id'
export GCLOUD_PROJECT='your-project-id'

# Google Cloud Region/Location for Vertex AI
export GOOGLE_CLOUD_REGION='us-central1'

# Google Application Default Credentials (ADC) path
# This will be set automatically after running: gcloud auth application-default login
export GOOGLE_APPLICATION_CREDENTIALS="$HOME/.config/gcloud/application_default_credentials.json"

# Vertex AI specific environment variables
export VERTEX_AI_PROJECT_ID='your-project-id'
export VERTEX_AI_LOCATION='us-central1'

# ============================================================================
# DEFAULT AI MODELS CONFIGURATION (VERTEX AI)
# ============================================================================

# Default LLM Configuration (Vertex AI Gemini)
export MINDSDB_DEFAULT_LLM_PROVIDER='vertex'
export MINDSDB_DEFAULT_LLM_MODEL='gemini-2.0-flash-exp'
export MINDSDB_DEFAULT_LLM_PROJECT_ID='your-project-id'
export MINDSDB_DEFAULT_LLM_LOCATION='us-central1'

# Default Embedding Model Configuration (Vertex AI)
export MINDSDB_DEFAULT_EMBEDDING_MODEL_PROVIDER='vertex'
export MINDSDB_DEFAULT_EMBEDDING_MODEL_NAME='text-embedding-004'
export MINDSDB_DEFAULT_EMBEDDING_MODEL_PROJECT_ID='your-project-id'
export MINDSDB_DEFAULT_EMBEDDING_MODEL_LOCATION='us-central1'

# Default Reranking Model Configuration (Vertex AI)
export MINDSDB_DEFAULT_RERANKING_MODEL_PROVIDER='vertex'
export MINDSDB_DEFAULT_RERANKING_MODEL_NAME='gemini-2.0-flash-exp'
export MINDSDB_DEFAULT_RERANKING_MODEL_PROJECT_ID='your-project-id'
export MINDSDB_DEFAULT_RERANKING_MODEL_LOCATION='us-central1'

# ============================================================================
# AUTHENTICATION & SECURITY
# ============================================================================

# MindsDB Basic Authentication (optional)
# export MINDSDB_USERNAME='admin'
# export MINDSDB_PASSWORD='secure_password_123'

# MCP Access Token for authentication
export MINDSDB_MCP_ACCESS_TOKEN='mcp_token_abc123'

# ============================================================================
# API CONFIGURATION
# ============================================================================

# HTTP API Configuration
export MINDSDB_HTTP_HOST='127.0.0.1'
export MINDSDB_HTTP_PORT='47334'

# MySQL API Configuration  
export MINDSDB_MYSQL_HOST='127.0.0.1'
export MINDSDB_MYSQL_PORT='47335'

# MongoDB API Configuration
export MINDSDB_MONGODB_HOST='127.0.0.1'
export MINDSDB_MONGODB_PORT='47336'

# PostgreSQL API Configuration
export MINDSDB_POSTGRES_HOST='127.0.0.1'
export MINDSDB_POSTGRES_PORT='55432'

# MCP API Configuration
export MINDSDB_MCP_HOST='127.0.0.1'
export MINDSDB_MCP_PORT='47337'

# ============================================================================
# OPTIONAL INTEGRATIONS & FEATURES
# ============================================================================

# OpenTelemetry Configuration (optional)
# export OTEL_EXPORTER_OTLP_ENDPOINT='http://localhost:4317'
# export OTEL_SERVICE_NAME='mindsdb'

# Prometheus Metrics (optional)
# export PROMETHEUS_MULTIPROC_DIR='/tmp/prometheus_multiproc_dir'

# Langfuse Tracing (optional)
# export LANGFUSE_PUBLIC_KEY='your_langfuse_public_key'
# export LANGFUSE_SECRET_KEY='your_langfuse_secret_key'
# export LANGFUSE_HOST='https://cloud.langfuse.com'

# ============================================================================
# DEVELOPMENT & DEBUGGING
# ============================================================================

# Enable development mode features
export MINDSDB_DEV_MODE='true'

# Flask Secret Key (auto-generated if not set)
export FLASK_SECRET_KEY='dev_secret_key_change_in_production'

# Arrow Memory Pool Configuration
export ARROW_DEFAULT_MEMORY_POOL='system'

# ============================================================================
# GOOGLE CLOUD AUTHENTICATION SETUP
# ============================================================================

echo ""
echo "🔐 Setting up Google Cloud Authentication..."

# Check if gcloud CLI is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI is not installed. Please install it first:"
    echo "   https://cloud.google.com/sdk/docs/install"
    echo ""
else
    echo "✅ gcloud CLI found"

    # Check if user is authenticated
    if gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
        echo "✅ User is authenticated with gcloud"
        CURRENT_USER=$(gcloud auth list --filter=status:ACTIVE --format="value(account)")
        echo "   Current user: $CURRENT_USER"
    else
        echo "⚠️  No active gcloud authentication found"
        echo "   Please run: gcloud auth login"
    fi

    # Check if Application Default Credentials are set up
    if [ -f "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
        echo "✅ Application Default Credentials found"
    else
        echo "⚠️  Application Default Credentials not found"
        echo "   Please run: gcloud auth application-default login"
        echo "   This is required for Vertex AI authentication"
    fi

    # Check current project
    CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null)
    if [ -n "$CURRENT_PROJECT" ]; then
        echo "✅ Current gcloud project: $CURRENT_PROJECT"
        if [ "$CURRENT_PROJECT" != "your-project-id" ]; then
            export GOOGLE_CLOUD_PROJECT="$CURRENT_PROJECT"
            export GCLOUD_PROJECT="$CURRENT_PROJECT"
            export VERTEX_AI_PROJECT_ID="$CURRENT_PROJECT"
            export MINDSDB_DEFAULT_LLM_PROJECT_ID="$CURRENT_PROJECT"
            export MINDSDB_DEFAULT_EMBEDDING_MODEL_PROJECT_ID="$CURRENT_PROJECT"
            export MINDSDB_DEFAULT_RERANKING_MODEL_PROJECT_ID="$CURRENT_PROJECT"
            echo "   Updated environment variables to use: $CURRENT_PROJECT"
        fi
    else
        echo "⚠️  No default gcloud project set"
        echo "   Please run: gcloud config set project YOUR_PROJECT_ID"
    fi
fi

echo ""
echo "🚀 Environment variables set successfully!"
echo ""
echo "📋 Active MindsDB Configuration:"
echo "- APIs: $MINDSDB_APIS"
echo "- HTTP API: http://$MINDSDB_HTTP_HOST:$MINDSDB_HTTP_PORT"
echo "- MySQL API: $MINDSDB_MYSQL_HOST:$MINDSDB_MYSQL_PORT"
echo "- MongoDB API: $MINDSDB_MONGODB_HOST:$MINDSDB_MONGODB_PORT"
echo "- PostgreSQL API: $MINDSDB_POSTGRES_HOST:$MINDSDB_POSTGRES_PORT"
echo "- MCP API: $MINDSDB_MCP_HOST:$MINDSDB_MCP_PORT"
echo "- MCP Access Token: $MINDSDB_MCP_ACCESS_TOKEN"
echo "- Log Level: $MINDSDB_LOG_LEVEL"
echo "- Default Project: $MINDSDB_DEFAULT_PROJECT"
echo ""
echo "🤖 Vertex AI Configuration:"
echo "- Project ID: $GOOGLE_CLOUD_PROJECT"
echo "- Region: $GOOGLE_CLOUD_REGION"
echo "- Default LLM: $MINDSDB_DEFAULT_LLM_MODEL"
echo "- Default Embedding: $MINDSDB_DEFAULT_EMBEDDING_MODEL_NAME"
echo "- Default Reranking: $MINDSDB_DEFAULT_RERANKING_MODEL_NAME"
echo ""

# Check if authentication setup is needed
if [ ! -f "$GOOGLE_APPLICATION_CREDENTIALS" ] || [ "$GOOGLE_CLOUD_PROJECT" = "your-project-id" ]; then
    echo "⚠️  SETUP REQUIRED:"
    echo ""
    echo "1. Authenticate with Google Cloud:"
    echo "   gcloud auth login"
    echo ""
    echo "2. Set your project ID:"
    echo "   gcloud config set project YOUR_PROJECT_ID"
    echo ""
    echo "3. Set up Application Default Credentials:"
    echo "   gcloud auth application-default login"
    echo ""
    echo "4. Enable required APIs:"
    echo "   gcloud services enable aiplatform.googleapis.com"
    echo "   gcloud services enable compute.googleapis.com"
    echo ""
    echo "5. Re-run this script after authentication"
    echo ""
fi
