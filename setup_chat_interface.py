#!/usr/bin/env python3
"""
MindsDB Chat Interface Setup Script
===================================

This script sets up the MindsDB Chat Interface (Respond) with Vertex AI integration.
It creates the necessary agents and configurations to enable conversational AI
with your data using the new Chat UI feature.

Features:
- Creates AI agents with Vertex AI models
- Sets up knowledge bases for semantic search
- Configures the chat interface for natural language queries
- Enables both parametric (SQL) and semantic queries through chat
"""

import requests
import json
import time
import sys
from typing import Dict, Any

# MindsDB API Configuration
MINDSDB_BASE_URL = "http://127.0.0.1:47334"
MINDSDB_SQL_URL = f"{MINDSDB_BASE_URL}/api/sql/query"

def execute_sql(query: str) -> Dict[str, Any]:
    """Execute SQL query against MindsDB"""
    try:
        response = requests.post(
            MINDSDB_SQL_URL,
            json={"query": query},
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        return {
            "success": response.status_code == 200,
            "data": response.json() if response.status_code == 200 else None,
            "error": response.text if response.status_code != 200 else None
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

def test_connection() -> bool:
    """Test basic MindsDB connection"""
    try:
        response = requests.get(MINDSDB_BASE_URL, timeout=10)
        return response.status_code == 200
    except:
        return False

def create_vertex_ai_engine() -> bool:
    """Create Vertex AI ML Engine for chat"""
    print("🚀 Creating Vertex AI ML Engine for chat...")
    
    # Drop existing engine if it exists
    drop_query = "DROP ML_ENGINE IF EXISTS vertex_chat_engine;"
    execute_sql(drop_query)
    
    # Create new engine with Vertex AI
    create_query = """
    CREATE ML_ENGINE vertex_chat_engine
    FROM vertex
    USING
        project_id = 'truxtsaas',
        location = 'us-central1';
    """
    
    result = execute_sql(create_query)
    if not result["success"]:
        print(f"❌ Failed to create Vertex AI engine: {result['error']}")
        return False
    
    print("✅ Vertex AI engine created successfully!")
    return True

def create_chat_agent() -> bool:
    """Create AI agent for chat interface"""
    print("🤖 Creating AI agent for chat interface...")
    
    # Drop existing agent if it exists
    drop_query = "DROP AGENT IF EXISTS chat_agent;"
    execute_sql(drop_query)
    
    # Create new agent
    create_query = """
    CREATE AGENT chat_agent
    USING
        model = 'vertex_chat_engine',
        skills = [],
        prompt_template = 'You are an intelligent data assistant powered by MindsDB and Vertex AI. 
        You can help users query their data using natural language. You have access to:
        
        1. SQL databases through parametric queries
        2. Knowledge bases through semantic search
        3. AI models for analysis and predictions
        
        When users ask questions:
        - For structured data queries, generate appropriate SQL
        - For document/text analysis, use semantic search
        - Provide clear, helpful responses with data insights
        - Explain your reasoning when helpful
        
        User question: {{question}}';
    """
    
    result = execute_sql(create_query)
    if not result["success"]:
        print(f"❌ Failed to create chat agent: {result['error']}")
        return False
    
    print("✅ Chat agent created successfully!")
    return True

def create_sample_knowledge_base() -> bool:
    """Create a sample knowledge base for demonstration"""
    print("📚 Creating sample knowledge base...")
    
    # Drop existing KB if it exists
    drop_query = "DROP KNOWLEDGE_BASE IF EXISTS sample_docs;"
    execute_sql(drop_query)
    
    # Create knowledge base with sample content
    create_query = """
    CREATE KNOWLEDGE_BASE sample_docs
    USING
        model = 'vertex_chat_engine',
        storage = 'vector_store',
        content = 'MindsDB is an AI data platform that enables you to connect, unify, and respond to your data using AI. 
        It supports various data sources including databases, APIs, and files. 
        With MindsDB, you can create AI models, agents, and chatbots that can understand and respond to natural language queries.
        
        Key features include:
        - Federated query engine for connecting multiple data sources
        - AI model integration with various providers
        - Knowledge bases for semantic search
        - Chat interface for conversational AI
        - Agents for automated workflows';
    """
    
    result = execute_sql(create_query)
    if not result["success"]:
        print(f"❌ Failed to create knowledge base: {result['error']}")
        return False
    
    print("✅ Sample knowledge base created successfully!")
    return True

def create_enhanced_agent() -> bool:
    """Create enhanced agent with knowledge base access"""
    print("🧠 Creating enhanced agent with knowledge base access...")
    
    # Drop existing agent if it exists
    drop_query = "DROP AGENT IF EXISTS enhanced_chat_agent;"
    execute_sql(drop_query)
    
    # Create enhanced agent with KB skills
    create_query = """
    CREATE AGENT enhanced_chat_agent
    USING
        model = 'vertex_chat_engine',
        skills = ['sample_docs'],
        prompt_template = 'You are an advanced AI assistant with access to both structured databases and knowledge bases.
        
        You can:
        1. Answer questions using SQL queries for structured data
        2. Search knowledge bases for contextual information
        3. Combine insights from multiple sources
        4. Provide comprehensive analysis and recommendations
        
        When responding:
        - Use clear, conversational language
        - Cite your sources when using knowledge base information
        - Suggest follow-up questions when appropriate
        - Explain complex concepts simply
        
        User question: {{question}}';
    """
    
    result = execute_sql(create_query)
    if not result["success"]:
        print(f"❌ Failed to create enhanced agent: {result['error']}")
        return False
    
    print("✅ Enhanced chat agent created successfully!")
    return True

def test_chat_functionality() -> bool:
    """Test the chat functionality"""
    print("🔍 Testing chat functionality...")
    
    # Test basic agent response
    test_query = """
    SELECT response 
    FROM enhanced_chat_agent 
    WHERE question = 'What is MindsDB and how can it help with AI data projects?'
    LIMIT 1;
    """
    
    result = execute_sql(test_query)
    if not result["success"]:
        print(f"❌ Chat test failed: {result['error']}")
        return False
    
    print("✅ Chat functionality test passed!")
    return True

def list_agents() -> bool:
    """List all available agents"""
    print("📋 Listing available agents...")
    
    query = "SHOW AGENTS;"
    result = execute_sql(query)
    
    if not result["success"]:
        print(f"❌ Failed to list agents: {result['error']}")
        return False
    
    print("✅ Available agents:")
    if result["data"] and "data" in result["data"]:
        for row in result["data"]["data"]:
            print(f"  - {row[0]} (Model: {row[1] if len(row) > 1 else 'N/A'})")
    
    return True

def print_chat_instructions():
    """Print instructions for using the chat interface"""
    print("\n" + "=" * 60)
    print("🎉 Chat Interface Setup Complete!")
    print("=" * 60)
    
    print("\n📋 **How to Access the Chat Interface:**")
    print("1. Open your browser and go to: http://127.0.0.1:47334/")
    print("2. Look for the robot icon (🤖) in the left sidebar menu")
    print("3. Click on the robot icon to access the Chat UI")
    print("4. You should see the 'Respond' interface")
    
    print("\n🚀 **Available Agents:**")
    print("- chat_agent: Basic conversational AI agent")
    print("- enhanced_chat_agent: Advanced agent with knowledge base access")
    
    print("\n💬 **Example Questions to Try:**")
    print("- 'What is MindsDB?'")
    print("- 'How can I connect to databases?'")
    print("- 'Explain AI agents and their capabilities'")
    print("- 'What are knowledge bases used for?'")
    
    print("\n🔧 **SQL Commands for Manual Testing:**")
    print("-- Test basic agent:")
    print("SELECT response FROM chat_agent WHERE question = 'Your question here';")
    print("\n-- Test enhanced agent:")
    print("SELECT response FROM enhanced_chat_agent WHERE question = 'Your question here';")
    
    print("\n📚 **Next Steps:**")
    print("1. Connect your own databases using CREATE DATABASE")
    print("2. Create knowledge bases with your documents")
    print("3. Build custom agents for specific use cases")
    print("4. Integrate with chat applications (Slack, Teams)")
    
    print("\n🌟 **Features Available:**")
    print("- ✅ Natural language to SQL conversion")
    print("- ✅ Semantic search over documents")
    print("- ✅ Multi-source data insights")
    print("- ✅ Conversational AI interface")
    print("- ✅ Vertex AI integration")

def main():
    """Main execution"""
    print("🚀 MindsDB Chat Interface Setup")
    print("=" * 50)
    
    # Test connection
    print("🔗 Testing MindsDB connection...")
    if not test_connection():
        print("❌ MindsDB is not accessible. Please ensure it's running.")
        sys.exit(1)
    print("✅ MindsDB connection successful")
    
    # Create Vertex AI engine
    print("\n" + "=" * 50)
    if not create_vertex_ai_engine():
        print("❌ Failed to create Vertex AI engine")
        return False
    
    # Create basic chat agent
    print("\n" + "=" * 50)
    if not create_chat_agent():
        print("❌ Failed to create chat agent")
        return False
    
    # Create sample knowledge base
    print("\n" + "=" * 50)
    if not create_sample_knowledge_base():
        print("❌ Failed to create knowledge base")
        return False
    
    # Create enhanced agent
    print("\n" + "=" * 50)
    if not create_enhanced_agent():
        print("❌ Failed to create enhanced agent")
        return False
    
    # List agents
    print("\n" + "=" * 50)
    list_agents()
    
    # Test functionality
    print("\n" + "=" * 50)
    if not test_chat_functionality():
        print("❌ Chat functionality test failed")
        return False
    
    # Print instructions
    print_chat_instructions()
    
    return True

if __name__ == "__main__":
    main()
