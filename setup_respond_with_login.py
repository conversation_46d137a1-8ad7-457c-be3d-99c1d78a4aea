#!/usr/bin/env python3
"""
Setup Respond Interface with Proper Authentication
==================================================

This script uses the login endpoint to authenticate and then sets up
the Respond chat interface.
"""

import requests
import json
import sys

MINDSDB_BASE_URL = "http://localhost:47334"
USERNAME = "mindsdb"
PASSWORD = "mindsdb123"

class MindsDBClient:
    def __init__(self, base_url: str, username: str, password: str):
        self.base_url = base_url
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.authenticated = False
    
    def login(self) -> bool:
        """Login to MindsDB and get session"""
        try:
            print("🔐 Logging into MindsDB...")
            
            login_url = f"{self.base_url}/api/login"
            login_data = {
                "username": self.username,
                "password": self.password
            }
            
            response = self.session.post(login_url, json=login_data, timeout=30)
            print(f"📊 Login response: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ Login successful!")
                self.authenticated = True
                
                # Check if we got any session cookies
                if self.session.cookies:
                    print(f"🍪 Session cookies: {list(self.session.cookies.keys())}")
                
                return True
            else:
                print(f"❌ Login failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Login error: {str(e)}")
            return False
    
    def execute_sql(self, query: str, description: str = "") -> dict:
        """Execute SQL query with authenticated session"""
        try:
            if not self.authenticated:
                print("❌ Not authenticated. Please login first.")
                return {"success": False, "error": "Not authenticated"}
            
            print(f"🔧 {description}")
            print(f"📝 Query: {query[:100]}...")
            
            sql_url = f"{self.base_url}/api/sql/query"
            response = self.session.post(
                sql_url,
                json={"query": query},
                headers={"Content-Type": "application/json"},
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Success: {description}")
                return {"success": True, "data": result}
            else:
                print(f"❌ Failed: {description}")
                print(f"📄 Error: {response.text}")
                return {"success": False, "error": response.text}
                
        except Exception as e:
            print(f"❌ Exception in {description}: {str(e)}")
            return {"success": False, "error": str(e)}

def setup_respond_interface(client: MindsDBClient) -> bool:
    """Set up the Respond chat interface"""
    print("\n🚀 Setting up MindsDB Respond Chat Interface")
    print("=" * 60)
    
    # List of SQL commands to execute
    commands = [
        {
            "query": "SHOW DATABASES;",
            "description": "Checking available databases",
            "required": False
        },
        {
            "query": "SHOW ML_ENGINES;",
            "description": "Checking available ML engines",
            "required": False
        },
        {
            "query": """
                CREATE MODEL IF NOT EXISTS simple_chat_model
                PREDICT response
                USING
                    prompt_template = 'You are a helpful AI assistant for MindsDB. You can help users query their data and understand AI concepts. Answer the following question clearly and concisely: {{question}}';
            """,
            "description": "Creating simple chat model",
            "required": True
        },
        {
            "query": """
                CREATE AGENT IF NOT EXISTS respond_chat_agent
                USING
                    model = 'simple_chat_model',
                    prompt_template = 'You are an intelligent data assistant powered by MindsDB. You can help users query their data using natural language, create AI models, and understand their data better. Please answer: {{question}}';
            """,
            "description": "Creating Respond chat agent",
            "required": True
        },
        {
            "query": "SHOW MODELS;",
            "description": "Listing created models",
            "required": False
        },
        {
            "query": "SHOW AGENTS;",
            "description": "Listing created agents",
            "required": False
        }
    ]
    
    # Execute commands
    success_count = 0
    required_success = 0
    total_required = sum(1 for cmd in commands if cmd["required"])
    
    for i, cmd in enumerate(commands, 1):
        print(f"\n📋 Step {i}/{len(commands)}: {cmd['description']}")
        result = client.execute_sql(cmd["query"], cmd["description"])
        
        if result["success"]:
            success_count += 1
            if cmd["required"]:
                required_success += 1
            
            # Print result data if available
            if "data" in result and result["data"]:
                data = result["data"]
                if isinstance(data, dict) and "data" in data:
                    print(f"📊 Result: {len(data['data'])} rows returned")
                    if data["data"] and len(data["data"]) <= 5:  # Show small results
                        for row in data["data"]:
                            print(f"  - {row}")
        
        import time
        time.sleep(1)  # Small delay between commands
    
    print(f"\n📊 Setup Results: {success_count}/{len(commands)} commands successful")
    print(f"📊 Required Commands: {required_success}/{total_required} successful")
    
    # Test the chat model if it was created
    if required_success >= 1:  # If at least the model creation succeeded
        print("\n🧪 Testing chat functionality...")
        test_query = """
            SELECT response 
            FROM simple_chat_model 
            WHERE question = 'Hello! What is MindsDB and how can it help with data analysis?' 
            LIMIT 1;
        """
        
        result = client.execute_sql(test_query, "Testing chat model")
        if result["success"]:
            print("✅ Chat model is working!")
            if "data" in result and result["data"] and "data" in result["data"]:
                response_data = result["data"]["data"]
                if response_data:
                    print(f"🤖 Sample response: {response_data[0][0][:200]}...")
        else:
            print("⚠️  Chat model test failed, but setup may still be successful")
    
    return required_success >= total_required

def check_web_interface() -> bool:
    """Check if the web interface is accessible"""
    print("\n🌐 Checking web interface...")
    
    try:
        response = requests.get(f"{MINDSDB_BASE_URL}/", timeout=10)
        if response.status_code == 200:
            print("✅ Web interface is accessible")
            
            # Check if it contains MindsDB content
            if "MindsDB" in response.text or "mindsdb" in response.text.lower():
                print("✅ MindsDB interface detected")
                return True
            else:
                print("⚠️  Web interface accessible but content unclear")
                return True
        else:
            print(f"❌ Web interface error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Web interface error: {str(e)}")
        return False

def print_final_instructions(setup_success: bool, web_ok: bool):
    """Print final instructions"""
    print("\n" + "=" * 60)
    print("🎉 MindsDB Respond Interface Setup Complete!")
    print("=" * 60)
    
    print(f"\n📊 **Setup Status:**")
    print(f"- SQL Setup: {'✅' if setup_success else '❌'}")
    print(f"- Web Interface: {'✅' if web_ok else '❌'}")
    
    print(f"\n🌐 **Access the Respond Chat Interface:**")
    print(f"1. Open: http://localhost:47334/")
    print(f"2. Look for: Robot icon (🤖), 'Respond', 'Chat', or 'AI Assistant' section")
    print(f"3. If you see a login screen, use: {USERNAME} / {PASSWORD}")
    
    if setup_success and web_ok:
        print(f"\n✅ **Expected Behavior:**")
        print(f"- The web interface should now show the Respond section")
        print(f"- You should be able to ask questions in natural language")
        print(f"- The chat agent should respond to your queries")
        print(f"- Models and agents have been created successfully")
    else:
        print(f"\n🔧 **Troubleshooting:**")
        if not setup_success:
            print(f"- SQL setup failed - check authentication and try again")
        if not web_ok:
            print(f"- Web interface not accessible - check container status")
        print(f"- Check container logs: docker logs mindsdb_chat")
        print(f"- Restart container: docker restart mindsdb_chat")
    
    print(f"\n🛠️  **Manual Testing:**")
    print(f"Test the chat model directly via SQL:")
    print(f"curl -X POST http://localhost:47334/api/login \\")
    print(f"  -H 'Content-Type: application/json' \\")
    print(f"  -d '{{\"username\": \"{USERNAME}\", \"password\": \"{PASSWORD}\"}}'")
    print(f"")
    print(f"Then use the session to query:")
    print(f"curl -X POST http://localhost:47334/api/sql/query \\")
    print(f"  -H 'Content-Type: application/json' \\")
    print(f"  -d '{{\"query\": \"SELECT response FROM simple_chat_model WHERE question = \\'Hello!\\' LIMIT 1;\"}}'")

def main():
    """Main execution"""
    print("🚀 MindsDB Respond Interface Setup with Authentication")
    print("=" * 60)
    
    # Create client and login
    client = MindsDBClient(MINDSDB_BASE_URL, USERNAME, PASSWORD)
    
    if not client.login():
        print("❌ Failed to login to MindsDB")
        return False
    
    # Set up the Respond interface
    setup_success = setup_respond_interface(client)
    
    # Check web interface
    web_ok = check_web_interface()
    
    # Print final instructions
    print_final_instructions(setup_success, web_ok)
    
    # Open browser if everything looks good
    if setup_success and web_ok:
        print(f"\n🌐 Opening web interface...")
        import webbrowser
        try:
            webbrowser.open(f"{MINDSDB_BASE_URL}/")
            print(f"✅ Browser opened to MindsDB interface")
        except:
            print(f"⚠️  Could not open browser automatically")
    
    return setup_success and web_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
