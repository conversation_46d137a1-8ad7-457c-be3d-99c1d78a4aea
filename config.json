{"version": "1.0", "description": "MindsDB Clean Configuration for Chat Interface", "api": {"http": {"host": "127.0.0.1", "port": "47334"}, "mysql": {"host": "127.0.0.1", "port": "47335"}, "mongodb": {"host": "127.0.0.1", "port": "47336"}, "postgres": {"host": "127.0.0.1", "port": "55432"}, "mcp": {"host": "127.0.0.1", "port": "47337"}}, "auth": {"http_auth_enabled": true, "username": "mindsdb", "password": "mindsdb123"}, "logging": {"level": "INFO"}}