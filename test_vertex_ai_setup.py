#!/usr/bin/env python3
"""
Test script to verify Vertex AI integration with MindsDB
This script tests the configuration and connectivity to Vertex AI services
"""

import requests
import json
import time
import sys

# MindsDB API endpoints
MINDSDB_HTTP_API = "http://127.0.0.1:47334"
MINDSDB_API_URL = f"{MINDSDB_HTTP_API}/api"

def test_mindsdb_connection():
    """Test basic MindsDB connection"""
    print("🔗 Testing MindsDB connection...")
    try:
        response = requests.get(f"{MINDSDB_API_URL}/status", timeout=10)
        if response.status_code == 200:
            print("✅ MindsDB is running and accessible")
            return True
        else:
            print(f"❌ MindsDB returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to connect to MindsDB: {e}")
        return False

def test_config_endpoint():
    """Test MindsDB configuration endpoint"""
    print("\n📋 Testing MindsDB configuration...")
    try:
        response = requests.get(f"{MINDSDB_API_URL}/config", timeout=10)
        if response.status_code == 200:
            config = response.json()
            print("✅ Configuration endpoint accessible")
            
            # Check default models configuration
            if 'default_llm' in config:
                print(f"✅ Default LLM configured: {config['default_llm']}")
            else:
                print("⚠️  No default LLM configuration found")
                
            if 'default_embedding_model' in config:
                print(f"✅ Default Embedding Model configured: {config['default_embedding_model']}")
            else:
                print("⚠️  No default embedding model configuration found")
                
            if 'default_reranking_model' in config:
                print(f"✅ Default Reranking Model configured: {config['default_reranking_model']}")
            else:
                print("⚠️  No default reranking model configuration found")
                
            return True
        else:
            print(f"❌ Config endpoint returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to access config endpoint: {e}")
        return False

def test_sql_query(query, description):
    """Execute a SQL query via MindsDB API"""
    print(f"\n🔍 Testing: {description}")
    try:
        headers = {'Content-Type': 'application/json'}
        data = {'query': query}
        
        response = requests.post(
            f"{MINDSDB_API_URL}/sql/query",
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'error' in result:
                print(f"❌ SQL Error: {result['error']}")
                return False
            else:
                print(f"✅ Query executed successfully")
                if 'data' in result and result['data']:
                    print(f"   Result: {result['data'][:2]}...")  # Show first 2 rows
                return True
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

def test_vertex_ai_integration():
    """Test Vertex AI integration by creating a simple ML engine"""
    print("\n🤖 Testing Vertex AI integration...")
    
    # Test creating a Vertex AI ML engine
    create_engine_query = """
    CREATE ML_ENGINE vertex_test_engine
    FROM vertex
    USING
        project_id = 'truxtsaas',
        location = 'us-central1'
    """
    
    if test_sql_query(create_engine_query, "Creating Vertex AI ML Engine"):
        print("✅ Vertex AI ML Engine created successfully")
        
        # Test listing ML engines
        list_engines_query = "SHOW ML_ENGINES"
        if test_sql_query(list_engines_query, "Listing ML Engines"):
            print("✅ ML Engines listed successfully")
            return True
    
    return False

def test_mcp_server():
    """Test MCP server connectivity"""
    print("\n🔌 Testing MCP server...")
    try:
        # Simple HTTP check to MCP port
        response = requests.get("http://127.0.0.1:47337/", timeout=5)
        print("✅ MCP server is accessible")
        return True
    except requests.exceptions.RequestException:
        print("❌ MCP server is not accessible")
        return False

def main():
    """Main test function"""
    print("🧪 MindsDB + Vertex AI Integration Test")
    print("=" * 50)
    
    # Wait a moment for MindsDB to fully start
    print("⏳ Waiting for MindsDB to fully initialize...")
    time.sleep(5)
    
    tests_passed = 0
    total_tests = 5
    
    # Test 1: Basic connection
    if test_mindsdb_connection():
        tests_passed += 1
    
    # Test 2: Configuration
    if test_config_endpoint():
        tests_passed += 1
    
    # Test 3: Basic SQL query
    if test_sql_query("SHOW DATABASES", "Basic SQL query (SHOW DATABASES)"):
        tests_passed += 1
    
    # Test 4: Vertex AI integration
    if test_vertex_ai_integration():
        tests_passed += 1
    
    # Test 5: MCP server
    if test_mcp_server():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! MindsDB + Vertex AI setup is working correctly.")
        print("\n🚀 You can now:")
        print("   - Access MindsDB GUI: http://127.0.0.1:47334/")
        print("   - Connect via MySQL: mysql -h 127.0.0.1 -P 47335 -u mindsdb")
        print("   - Use MCP server: http://127.0.0.1:47337/")
        print("   - Create Vertex AI models with default configuration")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the configuration.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
