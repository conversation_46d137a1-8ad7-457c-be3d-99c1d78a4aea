#!/usr/bin/env python3
"""
Simple MindsDB Chat Interface Setup
===================================

This script creates a basic chat interface setup using the default
Vertex AI configuration that's already working.
"""

import requests
import json
import time
import sys
from typing import Dict, Any

# MindsDB API Configuration
MINDSDB_BASE_URL = "http://127.0.0.1:47334"
MINDSDB_SQL_URL = f"{MINDSDB_BASE_URL}/api/sql/query"

def execute_sql(query: str) -> Dict[str, Any]:
    """Execute SQL query against MindsDB"""
    try:
        response = requests.post(
            MINDSDB_SQL_URL,
            json={"query": query},
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        return {
            "success": response.status_code == 200,
            "data": response.json() if response.status_code == 200 else None,
            "error": response.text if response.status_code != 200 else None
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

def test_connection() -> bool:
    """Test basic MindsDB connection"""
    try:
        response = requests.get(MINDSDB_BASE_URL, timeout=10)
        return response.status_code == 200
    except:
        return False

def create_simple_model() -> bool:
    """Create a simple chat model using default configuration"""
    print("🤖 Creating simple chat model...")
    
    # Create a simple model for chat
    create_query = """
    CREATE MODEL simple_chat_model
    PREDICT response
    USING
        engine = 'openai',
        model_name = 'gpt-3.5-turbo',
        prompt_template = 'You are a helpful AI assistant. Answer the following question: {{question}}';
    """
    
    result = execute_sql(create_query)
    if not result["success"]:
        print(f"❌ Failed to create chat model: {result['error']}")
        # Try with a different approach
        print("🔄 Trying alternative model creation...")
        
        alt_query = """
        CREATE MODEL simple_chat_model
        PREDICT response
        USING
            prompt_template = 'You are a helpful AI assistant. Answer the following question: {{question}}';
        """
        
        result = execute_sql(alt_query)
        if not result["success"]:
            print(f"❌ Alternative model creation also failed: {result['error']}")
            return False
    
    print("✅ Simple chat model created successfully!")
    return True

def test_model() -> bool:
    """Test the created model"""
    print("🔍 Testing chat model...")
    
    test_query = """
    SELECT response 
    FROM simple_chat_model 
    WHERE question = 'What is MindsDB?'
    LIMIT 1;
    """
    
    result = execute_sql(test_query)
    if not result["success"]:
        print(f"❌ Model test failed: {result['error']}")
        return False
    
    print("✅ Chat model test passed!")
    if result["data"] and "data" in result["data"] and result["data"]["data"]:
        response = result["data"]["data"][0][0]
        print(f"📝 Sample response: {response[:100]}...")
    
    return True

def check_existing_models() -> bool:
    """Check what models already exist"""
    print("📋 Checking existing models...")
    
    query = "SHOW MODELS;"
    result = execute_sql(query)
    
    if not result["success"]:
        print(f"❌ Failed to list models: {result['error']}")
        return False
    
    print("✅ Available models:")
    if result["data"] and "data" in result["data"]:
        for row in result["data"]["data"]:
            print(f"  - {row[0]} (Status: {row[3] if len(row) > 3 else 'Unknown'})")
    else:
        print("  No models found")
    
    return True

def print_instructions():
    """Print instructions for accessing the chat interface"""
    print("\n" + "=" * 60)
    print("🎉 Basic Chat Setup Information")
    print("=" * 60)
    
    print("\n📋 **How to Access the Chat Interface:**")
    print("1. Open your browser: http://127.0.0.1:47334/")
    print("2. Look for the robot icon (🤖) in the left sidebar")
    print("3. Click the robot icon to access the Chat UI")
    print("4. If you see a 'Respond' or 'Chat' interface, you're ready!")
    
    print("\n🔧 **Manual Testing:**")
    print("If you have a working model, test it with:")
    print("SELECT response FROM simple_chat_model WHERE question = 'Hello, how are you?';")
    
    print("\n⚠️  **If Chat Interface is Not Visible:**")
    print("The chat interface might not be available in this version of MindsDB.")
    print("Alternative approaches:")
    print("1. Use SQL queries directly to interact with AI models")
    print("2. Create chatbots for external platforms (Slack, Teams)")
    print("3. Use the MCP API for programmatic access")
    
    print("\n🚀 **Alternative: MCP API Chat**")
    print("You can use the MCP API for chat-like interactions:")
    print("URL: http://127.0.0.1:47337/")
    print("This provides a programmatic interface for AI interactions")
    
    print("\n📚 **Next Steps:**")
    print("1. Check if the robot icon appears in the web interface")
    print("2. If not available, use SQL queries for AI interactions")
    print("3. Consider creating chatbots for external platforms")
    print("4. Explore the MCP API for custom integrations")

def main():
    """Main execution"""
    print("🚀 Simple MindsDB Chat Interface Check")
    print("=" * 50)
    
    # Test connection
    print("🔗 Testing MindsDB connection...")
    if not test_connection():
        print("❌ MindsDB is not accessible. Please ensure it's running.")
        sys.exit(1)
    print("✅ MindsDB connection successful")
    
    # Check existing models
    print("\n" + "=" * 50)
    check_existing_models()
    
    # Try to create a simple model
    print("\n" + "=" * 50)
    if create_simple_model():
        # Test the model
        print("\n" + "=" * 50)
        test_model()
    
    # Print instructions
    print_instructions()
    
    return True

if __name__ == "__main__":
    main()
