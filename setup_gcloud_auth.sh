#!/bin/bash

# Google Cloud Authentication Setup Script for MindsDB + Vertex AI
# This script helps set up proper authentication for Vertex AI integration

echo "🔐 Google Cloud Authentication Setup for MindsDB + Vertex AI"
echo "=============================================================="
echo ""

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to prompt for user input
prompt_user() {
    read -p "$1: " response
    echo "$response"
}

# Step 1: Check if gcloud CLI is installed
echo "Step 1: Checking Google Cloud CLI installation..."
if ! command_exists gcloud; then
    echo "❌ Google Cloud CLI is not installed."
    echo ""
    echo "Please install it first:"
    echo "  macOS: brew install google-cloud-sdk"
    echo "  Linux: https://cloud.google.com/sdk/docs/install"
    echo "  Windows: https://cloud.google.com/sdk/docs/install"
    echo ""
    exit 1
else
    echo "✅ Google Cloud CLI is installed"
    gcloud version
fi

echo ""

# Step 2: User Authentication
echo "Step 2: User Authentication..."
if gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
    CURRENT_USER=$(gcloud auth list --filter=status:ACTIVE --format="value(account)")
    echo "✅ Already authenticated as: $CURRENT_USER"
    
    read -p "Do you want to re-authenticate? (y/N): " re_auth
    if [[ $re_auth =~ ^[Yy]$ ]]; then
        echo "🔄 Re-authenticating..."
        gcloud auth login
    fi
else
    echo "🔑 Authenticating with Google Cloud..."
    gcloud auth login
fi

echo ""

# Step 3: Project Configuration
echo "Step 3: Project Configuration..."
CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null)

if [ -n "$CURRENT_PROJECT" ]; then
    echo "✅ Current project: $CURRENT_PROJECT"
    read -p "Do you want to use this project? (Y/n): " use_current
    
    if [[ $use_current =~ ^[Nn]$ ]]; then
        PROJECT_ID=$(prompt_user "Enter your Google Cloud Project ID")
        gcloud config set project "$PROJECT_ID"
    else
        PROJECT_ID="$CURRENT_PROJECT"
    fi
else
    echo "⚠️  No project set"
    PROJECT_ID=$(prompt_user "Enter your Google Cloud Project ID")
    gcloud config set project "$PROJECT_ID"
fi

echo "📋 Using project: $PROJECT_ID"
echo ""

# Step 4: Application Default Credentials
echo "Step 4: Setting up Application Default Credentials..."
ADC_PATH="$HOME/.config/gcloud/application_default_credentials.json"

if [ -f "$ADC_PATH" ]; then
    echo "✅ Application Default Credentials already exist"
    read -p "Do you want to refresh them? (y/N): " refresh_adc
    
    if [[ $refresh_adc =~ ^[Yy]$ ]]; then
        echo "🔄 Refreshing Application Default Credentials..."
        gcloud auth application-default login
    fi
else
    echo "🔑 Setting up Application Default Credentials..."
    echo "This is required for Vertex AI authentication in MindsDB"
    gcloud auth application-default login
fi

echo ""

# Step 5: Enable Required APIs
echo "Step 5: Enabling required Google Cloud APIs..."
echo "🔄 Enabling Vertex AI API..."
gcloud services enable aiplatform.googleapis.com

echo "🔄 Enabling Compute Engine API..."
gcloud services enable compute.googleapis.com

echo "🔄 Enabling Cloud Resource Manager API..."
gcloud services enable cloudresourcemanager.googleapis.com

echo ""

# Step 6: Verify Setup
echo "Step 6: Verifying setup..."

# Check authentication
if gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
    echo "✅ User authentication: OK"
else
    echo "❌ User authentication: FAILED"
fi

# Check project
if [ "$(gcloud config get-value project 2>/dev/null)" = "$PROJECT_ID" ]; then
    echo "✅ Project configuration: OK ($PROJECT_ID)"
else
    echo "❌ Project configuration: FAILED"
fi

# Check ADC
if [ -f "$ADC_PATH" ]; then
    echo "✅ Application Default Credentials: OK"
else
    echo "❌ Application Default Credentials: FAILED"
fi

# Check API enablement
echo "🔄 Checking API enablement..."
if gcloud services list --enabled --filter="name:aiplatform.googleapis.com" --format="value(name)" | grep -q "aiplatform"; then
    echo "✅ Vertex AI API: Enabled"
else
    echo "⚠️  Vertex AI API: Not enabled or still enabling"
fi

echo ""

# Step 7: Update MindsDB Configuration
echo "Step 7: Updating MindsDB configuration..."

# Update the environment script with the actual project ID
if [ -f "setup_mindsdb_env.sh" ]; then
    echo "🔄 Updating setup_mindsdb_env.sh with project ID: $PROJECT_ID"
    sed -i.bak "s/your-project-id/$PROJECT_ID/g" setup_mindsdb_env.sh
    echo "✅ Environment script updated"
fi

# Update the config.json with the actual project ID
if [ -f "config.json" ]; then
    echo "🔄 Updating config.json with project ID: $PROJECT_ID"
    sed -i.bak "s/your-project-id/$PROJECT_ID/g" config.json
    echo "✅ Configuration file updated"
fi

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "Your Google Cloud authentication is now configured for MindsDB + Vertex AI:"
echo "- Project ID: $PROJECT_ID"
echo "- User: $(gcloud auth list --filter=status:ACTIVE --format="value(account)")"
echo "- ADC Path: $ADC_PATH"
echo ""
echo "Next steps:"
echo "1. Run: source setup_mindsdb_env.sh"
echo "2. Start MindsDB: python -m mindsdb --config config.json"
echo ""
echo "You can now use Vertex AI models as the default in MindsDB!"
echo ""
