#!/bin/bash

# ============================================================================
# MindsDB Docker Setup Script with Chat Interface Support
# ============================================================================

set -e  # Exit on any error

echo "🚀 MindsDB Docker Setup with Chat Interface Support"
echo "=" * 60

# Configuration
CONTAINER_NAME="mindsdb_chat"
IMAGE_NAME="mindsdb/mindsdb"
MINDSDB_USERNAME="mindsdb"
MINDSDB_PASSWORD="mindsdb123"
MCP_ACCESS_TOKEN="mcp_token_abc123"

# Ports
HTTP_PORT="47334"
MYSQL_PORT="47335"
MONGODB_PORT="47336"
POSTGRES_PORT="55432"
MCP_PORT="47337"

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT="truxtsaas"
GOOGLE_CLOUD_REGION="us-central1"

echo "📋 Configuration:"
echo "- Container Name: $CONTAINER_NAME"
echo "- Image: $IMAGE_NAME"
echo "- Username: $MINDSDB_USERNAME"
echo "- Password: $MINDSDB_PASSWORD"
echo "- MCP Token: $MCP_ACCESS_TOKEN"
echo "- Google Cloud Project: $GOOGLE_CLOUD_PROJECT"
echo ""

# Function to check if Docker is running
check_docker() {
    echo "🔍 Checking Docker..."
    if ! docker --version > /dev/null 2>&1; then
        echo "❌ Docker is not installed or not running"
        echo "Please install Docker Desktop and start it"
        exit 1
    fi
    echo "✅ Docker is available"
}

# Function to stop and remove existing container
cleanup_existing() {
    echo "🧹 Cleaning up existing containers..."
    
    if docker ps -a --format 'table {{.Names}}' | grep -q "^$CONTAINER_NAME$"; then
        echo "🛑 Stopping existing container: $CONTAINER_NAME"
        docker stop $CONTAINER_NAME || true
        echo "🗑️  Removing existing container: $CONTAINER_NAME"
        docker rm $CONTAINER_NAME || true
    fi
    
    echo "✅ Cleanup complete"
}

# Function to pull latest MindsDB image
pull_image() {
    echo "📥 Pulling latest MindsDB Docker image..."
    docker pull $IMAGE_NAME
    echo "✅ Image pulled successfully"
}

# Function to check Google Cloud authentication
check_gcloud_auth() {
    echo "🔐 Checking Google Cloud authentication..."
    
    if command -v gcloud > /dev/null 2>&1; then
        echo "✅ gcloud CLI found"
        
        # Check if user is authenticated
        if gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 > /dev/null 2>&1; then
            ACTIVE_ACCOUNT=$(gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1)
            echo "✅ Authenticated as: $ACTIVE_ACCOUNT"
            
            # Check current project
            CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null || echo "")
            if [ "$CURRENT_PROJECT" = "$GOOGLE_CLOUD_PROJECT" ]; then
                echo "✅ Project set to: $CURRENT_PROJECT"
            else
                echo "⚠️  Current project: $CURRENT_PROJECT (expected: $GOOGLE_CLOUD_PROJECT)"
                echo "💡 Run: gcloud config set project $GOOGLE_CLOUD_PROJECT"
            fi
        else
            echo "⚠️  Not authenticated with gcloud"
            echo "💡 Run: gcloud auth login"
        fi
    else
        echo "⚠️  gcloud CLI not found"
        echo "💡 Install Google Cloud SDK for Vertex AI integration"
    fi
}

# Function to create Google Cloud credentials volume
setup_gcloud_volume() {
    echo "🔑 Setting up Google Cloud credentials..."
    
    # Check if Application Default Credentials exist
    ADC_PATH="$HOME/.config/gcloud/application_default_credentials.json"
    if [ -f "$ADC_PATH" ]; then
        echo "✅ Application Default Credentials found"
        GCLOUD_VOLUME="-v $HOME/.config/gcloud:/root/.config/gcloud:ro"
        echo "📁 Will mount: $HOME/.config/gcloud"
    else
        echo "⚠️  Application Default Credentials not found"
        echo "💡 Run: gcloud auth application-default login"
        GCLOUD_VOLUME=""
    fi
}

# Function to start MindsDB container
start_container() {
    echo "🚀 Starting MindsDB container..."
    
    # Build the docker run command
    DOCKER_CMD="docker run -d \
        --name $CONTAINER_NAME \
        -e MINDSDB_APIS='http,mysql,mongodb,postgres,mcp' \
        -e MINDSDB_USERNAME='$MINDSDB_USERNAME' \
        -e MINDSDB_PASSWORD='$MINDSDB_PASSWORD' \
        -e MINDSDB_MCP_ACCESS_TOKEN='$MCP_ACCESS_TOKEN' \
        -e GOOGLE_CLOUD_PROJECT='$GOOGLE_CLOUD_PROJECT' \
        -e GOOGLE_CLOUD_REGION='$GOOGLE_CLOUD_REGION' \
        -e MINDSDB_LOG_LEVEL='INFO' \
        -p $HTTP_PORT:47334 \
        -p $MYSQL_PORT:47335 \
        -p $MONGODB_PORT:47336 \
        -p $POSTGRES_PORT:55432 \
        -p $MCP_PORT:47337"
    
    # Add Google Cloud volume if available
    if [ -n "$GCLOUD_VOLUME" ]; then
        DOCKER_CMD="$DOCKER_CMD $GCLOUD_VOLUME"
    fi
    
    # Add image name
    DOCKER_CMD="$DOCKER_CMD $IMAGE_NAME"
    
    echo "🔧 Running command:"
    echo "$DOCKER_CMD"
    echo ""
    
    # Execute the command
    eval $DOCKER_CMD
    
    CONTAINER_ID=$(docker ps -q -f name=$CONTAINER_NAME)
    if [ -n "$CONTAINER_ID" ]; then
        echo "✅ Container started successfully!"
        echo "📦 Container ID: $CONTAINER_ID"
    else
        echo "❌ Failed to start container"
        exit 1
    fi
}

# Function to wait for MindsDB to be ready
wait_for_mindsdb() {
    echo "⏳ Waiting for MindsDB to be ready..."
    
    MAX_ATTEMPTS=30
    ATTEMPT=1
    
    while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
        echo "🔍 Attempt $ATTEMPT/$MAX_ATTEMPTS..."
        
        if curl -s http://localhost:$HTTP_PORT/ > /dev/null 2>&1; then
            echo "✅ MindsDB HTTP API is ready!"
            break
        fi
        
        if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
            echo "❌ MindsDB failed to start within expected time"
            echo "📋 Container logs:"
            docker logs $CONTAINER_NAME --tail 20
            exit 1
        fi
        
        sleep 5
        ATTEMPT=$((ATTEMPT + 1))
    done
}

# Function to test APIs
test_apis() {
    echo "🧪 Testing APIs..."
    
    # Test HTTP API
    echo "🌐 Testing HTTP API..."
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:$HTTP_PORT/)
    if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "401" ]; then
        echo "✅ HTTP API responding (Status: $HTTP_STATUS)"
    else
        echo "❌ HTTP API not responding (Status: $HTTP_STATUS)"
    fi
    
    # Test MCP API
    echo "🤖 Testing MCP API..."
    MCP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer $MCP_ACCESS_TOKEN" http://localhost:$MCP_PORT/sse)
    if [ "$MCP_STATUS" = "200" ]; then
        echo "✅ MCP API responding with authentication"
    else
        echo "⚠️  MCP API status: $MCP_STATUS"
    fi
}

# Function to print access information
print_access_info() {
    echo ""
    echo "🎉 MindsDB Docker Setup Complete!"
    echo "=" * 60
    
    echo ""
    echo "🌐 **Web Interface Access:**"
    echo "URL: http://localhost:$HTTP_PORT/"
    echo "Username: $MINDSDB_USERNAME"
    echo "Password: $MINDSDB_PASSWORD"
    
    echo ""
    echo "🤖 **MCP API Access:**"
    echo "URL: http://localhost:$MCP_PORT/sse"
    echo "Authorization: Bearer $MCP_ACCESS_TOKEN"
    
    echo ""
    echo "🔌 **All Available APIs:**"
    echo "- HTTP API: http://localhost:$HTTP_PORT/ (Web Interface)"
    echo "- MySQL API: localhost:$MYSQL_PORT (SQL Access)"
    echo "- MongoDB API: localhost:$MONGODB_PORT (NoSQL Access)"
    echo "- PostgreSQL API: localhost:$POSTGRES_PORT (SQL Access)"
    echo "- MCP API: localhost:$MCP_PORT (Chat Interface Protocol)"
    
    echo ""
    echo "📋 **How to Access Chat Interface:**"
    echo "1. Open: http://localhost:$HTTP_PORT/"
    echo "2. Login with: $MINDSDB_USERNAME / $MINDSDB_PASSWORD"
    echo "3. Look for: Robot icon (🤖) or 'Respond' section"
    echo "4. Start chatting with your data!"
    
    echo ""
    echo "🛠️  **Container Management:**"
    echo "- View logs: docker logs $CONTAINER_NAME"
    echo "- Stop container: docker stop $CONTAINER_NAME"
    echo "- Start container: docker start $CONTAINER_NAME"
    echo "- Remove container: docker rm $CONTAINER_NAME"
    
    echo ""
    echo "🔧 **Troubleshooting:**"
    echo "- Check logs: docker logs $CONTAINER_NAME --tail 50"
    echo "- Access container: docker exec -it $CONTAINER_NAME bash"
    echo "- Restart container: docker restart $CONTAINER_NAME"
}

# Main execution
main() {
    check_docker
    cleanup_existing
    pull_image
    check_gcloud_auth
    setup_gcloud_volume
    start_container
    wait_for_mindsdb
    test_apis
    print_access_info
}

# Run main function
main
