#!/usr/bin/env python3
"""
MindsDB MCP Client Setup and Testing
====================================

This script sets up and tests MCP (Model Context Protocol) clients
to enable the Respond chat interface in MindsDB.
"""

import requests
import json
import time
import sys
from typing import Dict, Any, Optional

# MCP Configuration
MCP_BASE_URL = "http://localhost:47337"
MCP_ACCESS_TOKEN = "mcp_token_abc123"
MINDSDB_BASE_URL = "http://localhost:47334"

class MCPClient:
    def __init__(self, base_url: str, access_token: str):
        self.base_url = base_url
        self.access_token = access_token
        self.session_id = None
        
    def get_headers(self) -> Dict[str, str]:
        """Get authentication headers for MCP requests"""
        return {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
    
    def initialize_session(self) -> bool:
        """Initialize MCP session"""
        try:
            print("🔗 Initializing MCP session...")
            
            # Try to get SSE endpoint first
            response = requests.get(
                f"{self.base_url}/sse",
                headers=self.get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ MCP SSE endpoint accessible")
                # Extract session ID from response if available
                content = response.text
                if "session_id=" in content:
                    import re
                    match = re.search(r'session_id=([a-f0-9]+)', content)
                    if match:
                        self.session_id = match.group(1)
                        print(f"📋 Session ID: {self.session_id}")
                return True
            else:
                print(f"❌ MCP SSE endpoint failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ MCP session initialization failed: {str(e)}")
            return False
    
    def test_tools(self) -> bool:
        """Test available MCP tools"""
        try:
            print("🛠️  Testing MCP tools...")
            
            # Test list_databases tool
            payload = {
                "method": "tools/call",
                "params": {
                    "name": "list_databases",
                    "arguments": {}
                }
            }
            
            response = requests.post(
                f"{self.base_url}/",
                headers=self.get_headers(),
                json=payload,
                timeout=30
            )
            
            print(f"📊 List databases response: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Databases: {json.dumps(result, indent=2)}")
                return True
            else:
                print(f"❌ Tools test failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ MCP tools test failed: {str(e)}")
            return False

def test_mindsdb_web_auth() -> bool:
    """Test MindsDB web interface authentication"""
    try:
        print("🌐 Testing MindsDB web interface...")
        
        # Test basic access
        response = requests.get(MINDSDB_BASE_URL, timeout=10)
        print(f"📊 Web interface response: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Web interface accessible without auth")
            return True
        elif response.status_code == 401:
            print("🔐 Web interface requires authentication")
            
            # Try with basic auth
            auth_response = requests.get(
                MINDSDB_BASE_URL,
                auth=("mindsdb", "mindsdb123"),
                timeout=10
            )
            
            print(f"📊 Auth response: {auth_response.status_code}")
            if auth_response.status_code == 200:
                print("✅ Web interface accessible with auth")
                return True
            else:
                print("❌ Authentication failed")
                return False
        else:
            print(f"⚠️  Unexpected response: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Web interface test failed: {str(e)}")
        return False

def create_mcp_config() -> bool:
    """Create MCP configuration for web interface"""
    try:
        print("⚙️  Creating MCP configuration...")
        
        # Create MCP client configuration
        mcp_config = {
            "mcp": {
                "enabled": True,
                "server_url": MCP_BASE_URL,
                "access_token": MCP_ACCESS_TOKEN,
                "tools": ["list_databases", "query"],
                "timeout": 30
            },
            "respond": {
                "enabled": True,
                "mcp_integration": True,
                "default_model": "vertex",
                "authentication_required": True
            }
        }
        
        print("📝 MCP Configuration:")
        print(json.dumps(mcp_config, indent=2))
        
        # Save configuration to file
        with open("mcp_config.json", "w") as f:
            json.dump(mcp_config, f, indent=2)
        
        print("✅ MCP configuration saved to mcp_config.json")
        return True
        
    except Exception as e:
        print(f"❌ MCP configuration creation failed: {str(e)}")
        return False

def setup_respond_interface() -> bool:
    """Set up the Respond chat interface configuration"""
    try:
        print("🤖 Setting up Respond interface...")
        
        # Create SQL commands to enable Respond interface
        sql_commands = [
            # Create a simple model for testing
            """
            CREATE MODEL IF NOT EXISTS respond_test_model
            PREDICT response
            USING
                engine = 'openai',
                model_name = 'gpt-3.5-turbo',
                prompt_template = 'You are a helpful AI assistant. Answer: {{question}}';
            """,
            
            # Create an agent for the Respond interface
            """
            CREATE AGENT IF NOT EXISTS respond_agent
            USING
                model = 'respond_test_model',
                prompt_template = 'You are an intelligent data assistant. Help users with: {{question}}';
            """
        ]
        
        print("📋 SQL commands to execute:")
        for i, cmd in enumerate(sql_commands, 1):
            print(f"{i}. {cmd.strip()}")
        
        # Save SQL commands to file
        with open("setup_respond.sql", "w") as f:
            for cmd in sql_commands:
                f.write(cmd.strip() + "\n\n")
        
        print("✅ Respond setup commands saved to setup_respond.sql")
        return True
        
    except Exception as e:
        print(f"❌ Respond interface setup failed: {str(e)}")
        return False

def create_docker_mcp_config() -> bool:
    """Create Docker-specific MCP configuration"""
    try:
        print("🐳 Creating Docker MCP configuration...")
        
        # Create environment variables for Docker restart
        docker_env = f"""
# Docker Environment Variables for MCP
MINDSDB_APIS=http,mysql,mongodb,postgres,mcp
MINDSDB_USERNAME=mindsdb
MINDSDB_PASSWORD=mindsdb123
MINDSDB_MCP_ACCESS_TOKEN={MCP_ACCESS_TOKEN}
GOOGLE_CLOUD_PROJECT=truxtsaas
GOOGLE_CLOUD_REGION=us-central1
MINDSDB_LOG_LEVEL=INFO

# MCP Specific Configuration
MINDSDB_MCP_ENABLED=true
MINDSDB_MCP_TOOLS=list_databases,query
MINDSDB_RESPOND_ENABLED=true
"""
        
        with open("docker_mcp.env", "w") as f:
            f.write(docker_env)
        
        print("✅ Docker MCP environment saved to docker_mcp.env")
        
        # Create Docker restart command
        restart_cmd = f"""
# Restart MindsDB container with MCP configuration
docker stop mindsdb_chat
docker rm mindsdb_chat

docker run -d \\
    --name mindsdb_chat \\
    --env-file docker_mcp.env \\
    -v /Users/<USER>/.config/gcloud:/root/.config/gcloud:ro \\
    -p 47334:47334 \\
    -p 47335:47335 \\
    -p 47336:47336 \\
    -p 55432:55432 \\
    -p 47337:47337 \\
    mindsdb/mindsdb
"""
        
        with open("restart_docker_mcp.sh", "w") as f:
            f.write(restart_cmd)
        
        print("✅ Docker restart script saved to restart_docker_mcp.sh")
        return True
        
    except Exception as e:
        print(f"❌ Docker MCP configuration failed: {str(e)}")
        return False

def print_instructions():
    """Print instructions for accessing the Respond interface"""
    print("\n" + "=" * 60)
    print("🎉 MCP Client Setup Complete!")
    print("=" * 60)
    
    print("\n📋 **How to Access the Respond Chat Interface:**")
    print("1. Open: http://localhost:47334/")
    print("2. Login with: mindsdb / mindsdb123")
    print("3. Look for: Robot icon (🤖) or 'Respond' section")
    print("4. If not visible, try refreshing the page")
    
    print("\n🔧 **If Respond Interface is Still Not Visible:**")
    print("1. Execute the SQL commands in setup_respond.sql")
    print("2. Restart the Docker container with:")
    print("   chmod +x restart_docker_mcp.sh && ./restart_docker_mcp.sh")
    print("3. Check container logs: docker logs mindsdb_chat")
    
    print("\n🛠️  **Manual MCP Testing:**")
    print("Test MCP API directly:")
    print(f"curl -H 'Authorization: Bearer {MCP_ACCESS_TOKEN}' {MCP_BASE_URL}/sse")
    
    print("\n📚 **Configuration Files Created:**")
    print("- mcp_config.json: MCP client configuration")
    print("- setup_respond.sql: SQL commands for Respond interface")
    print("- docker_mcp.env: Docker environment variables")
    print("- restart_docker_mcp.sh: Docker restart script")
    
    print("\n🎯 **Expected Behavior:**")
    print("- Web interface should show login screen")
    print("- After login, look for chat/respond functionality")
    print("- MCP API should respond to authenticated requests")
    print("- Respond interface should allow natural language queries")

def main():
    """Main execution"""
    print("🚀 MindsDB MCP Client Setup")
    print("=" * 50)
    
    # Test MindsDB web interface
    print("\n🔍 Testing MindsDB web interface...")
    web_ok = test_mindsdb_web_auth()
    
    # Initialize MCP client
    print("\n🔍 Testing MCP client...")
    mcp_client = MCPClient(MCP_BASE_URL, MCP_ACCESS_TOKEN)
    mcp_session_ok = mcp_client.initialize_session()
    
    if mcp_session_ok:
        mcp_tools_ok = mcp_client.test_tools()
    else:
        mcp_tools_ok = False
    
    # Create configurations
    print("\n⚙️  Creating configurations...")
    config_ok = create_mcp_config()
    respond_ok = setup_respond_interface()
    docker_ok = create_docker_mcp_config()
    
    # Print results
    print("\n📊 **Setup Results:**")
    print(f"- Web Interface: {'✅' if web_ok else '❌'}")
    print(f"- MCP Session: {'✅' if mcp_session_ok else '❌'}")
    print(f"- MCP Tools: {'✅' if mcp_tools_ok else '❌'}")
    print(f"- MCP Config: {'✅' if config_ok else '❌'}")
    print(f"- Respond Setup: {'✅' if respond_ok else '❌'}")
    print(f"- Docker Config: {'✅' if docker_ok else '❌'}")
    
    # Print instructions
    print_instructions()
    
    return all([web_ok, mcp_session_ok, config_ok, respond_ok, docker_ok])

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
