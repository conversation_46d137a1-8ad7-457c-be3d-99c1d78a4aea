#!/usr/bin/env python3
"""
Gemini 2.5 ML Engine Setup Script
=================================

This script creates ML engines for Gemini 2.5 models with proper naming
to avoid SQL parsing issues with dots in model names.

The script creates engines with safe names that can be used in SQL queries:
- gemini_25_flash_engine (for gemini-2.5-flash-preview)
- gemini_25_pro_engine (for gemini-2.5-pro-preview)
"""

import requests
import json
import time
import sys
from typing import Dict, Any

# MindsDB API Configuration
MINDSDB_BASE_URL = "http://127.0.0.1:47334"
MINDSDB_SQL_URL = f"{MINDSDB_BASE_URL}/api/sql/query"

def execute_sql(query: str) -> Dict[str, Any]:
    """Execute SQL query against MindsDB"""
    try:
        response = requests.post(
            MINDSDB_SQL_URL,
            json={"query": query},
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        return {
            "success": response.status_code == 200,
            "data": response.json() if response.status_code == 200 else None,
            "error": response.text if response.status_code != 200 else None
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

def test_connection() -> bool:
    """Test basic MindsDB connection"""
    try:
        response = requests.get(MINDSDB_BASE_URL, timeout=10)
        return response.status_code == 200
    except:
        return False

def create_gemini_25_flash_engine() -> bool:
    """Create Gemini 2.5 Flash ML Engine"""
    print("🚀 Creating Gemini 2.5 Flash ML Engine...")
    
    # Drop existing engine if it exists
    drop_query = "DROP ML_ENGINE IF EXISTS gemini_25_flash_engine;"
    execute_sql(drop_query)
    
    # Create new engine with Gemini 2.5 Flash
    create_query = """
    CREATE ML_ENGINE gemini_25_flash_engine
    FROM vertex
    USING
        model_name = 'gemini-2.5-flash-preview',
        project_id = 'truxtsaas',
        location = 'us-central1',
        temperature = 0.7,
        max_tokens = 32768,
        context_window = 1000000;
    """
    
    result = execute_sql(create_query)
    if not result["success"]:
        print(f"❌ Failed to create Gemini 2.5 Flash engine: {result['error']}")
        return False
    
    print("✅ Gemini 2.5 Flash engine created successfully!")
    return True

def create_gemini_25_pro_engine() -> bool:
    """Create Gemini 2.5 Pro ML Engine"""
    print("🧠 Creating Gemini 2.5 Pro ML Engine...")
    
    # Drop existing engine if it exists
    drop_query = "DROP ML_ENGINE IF EXISTS gemini_25_pro_engine;"
    execute_sql(drop_query)
    
    # Create new engine with Gemini 2.5 Pro
    create_query = """
    CREATE ML_ENGINE gemini_25_pro_engine
    FROM vertex
    USING
        model_name = 'gemini-2.5-pro-preview',
        project_id = 'truxtsaas',
        location = 'us-central1',
        temperature = 0.3,
        max_tokens = 8192,
        context_window = 2000000;
    """
    
    result = execute_sql(create_query)
    if not result["success"]:
        print(f"❌ Failed to create Gemini 2.5 Pro engine: {result['error']}")
        return False
    
    print("✅ Gemini 2.5 Pro engine created successfully!")
    return True

def create_test_models() -> bool:
    """Create test models using the Gemini 2.5 engines"""
    print("🧪 Creating test models...")
    
    # Create a test model with Gemini 2.5 Flash
    flash_model_query = """
    CREATE MODEL gemini_25_flash_test
    PREDICT response
    USING
        engine = 'gemini_25_flash_engine',
        prompt_template = 'You are an advanced AI assistant with enhanced reasoning capabilities and a 1M+ token context window. Please provide a helpful and detailed response to: {{user_input}}';
    """
    
    result = execute_sql(flash_model_query)
    if not result["success"]:
        print(f"❌ Failed to create Flash test model: {result['error']}")
        return False
    
    # Create a test model with Gemini 2.5 Pro
    pro_model_query = """
    CREATE MODEL gemini_25_pro_test
    PREDICT analysis
    USING
        engine = 'gemini_25_pro_engine',
        prompt_template = 'You are an advanced reasoning AI with 2M+ token context window. Think step-by-step and provide comprehensive analysis for: {{complex_query}}';
    """
    
    result = execute_sql(pro_model_query)
    if not result["success"]:
        print(f"❌ Failed to create Pro test model: {result['error']}")
        return False
    
    print("✅ Test models created successfully!")
    return True

def test_models() -> bool:
    """Test the created models"""
    print("🔍 Testing Gemini 2.5 models...")
    
    # Test Flash model
    flash_test_query = """
    SELECT response 
    FROM gemini_25_flash_test 
    WHERE user_input = 'Explain the key benefits of Gemini 2.5 Flash compared to previous models'
    LIMIT 1;
    """
    
    result = execute_sql(flash_test_query)
    if not result["success"]:
        print(f"❌ Flash model test failed: {result['error']}")
        return False
    
    print("✅ Gemini 2.5 Flash model test passed!")
    
    # Test Pro model
    pro_test_query = """
    SELECT analysis 
    FROM gemini_25_pro_test 
    WHERE complex_query = 'Analyze the implications of large context windows for enterprise AI applications'
    LIMIT 1;
    """
    
    result = execute_sql(pro_test_query)
    if not result["success"]:
        print(f"❌ Pro model test failed: {result['error']}")
        return False
    
    print("✅ Gemini 2.5 Pro model test passed!")
    return True

def list_engines() -> bool:
    """List all ML engines"""
    print("📋 Listing ML engines...")
    
    query = "SHOW ML_ENGINES;"
    result = execute_sql(query)
    
    if not result["success"]:
        print(f"❌ Failed to list engines: {result['error']}")
        return False
    
    print("✅ Available ML engines:")
    if result["data"] and "data" in result["data"]:
        for row in result["data"]["data"]:
            print(f"  - {row[0]} ({row[1]})")
    
    return True

def main():
    """Main execution"""
    print("🚀 Gemini 2.5 ML Engine Setup")
    print("=" * 50)
    
    # Test connection
    print("🔗 Testing MindsDB connection...")
    if not test_connection():
        print("❌ MindsDB is not accessible. Please ensure it's running.")
        sys.exit(1)
    print("✅ MindsDB connection successful")
    
    # Create engines
    print("\n" + "=" * 50)
    if not create_gemini_25_flash_engine():
        print("❌ Failed to create Gemini 2.5 Flash engine")
        return False
    
    print("\n" + "=" * 50)
    if not create_gemini_25_pro_engine():
        print("❌ Failed to create Gemini 2.5 Pro engine")
        return False
    
    # Create test models
    print("\n" + "=" * 50)
    if not create_test_models():
        print("❌ Failed to create test models")
        return False
    
    # List engines
    print("\n" + "=" * 50)
    list_engines()
    
    # Test models
    print("\n" + "=" * 50)
    if not test_models():
        print("❌ Model tests failed")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 Gemini 2.5 setup completed successfully!")
    print("\n📋 Available engines:")
    print("- gemini_25_flash_engine (Gemini 2.5 Flash Preview)")
    print("- gemini_25_pro_engine (Gemini 2.5 Pro Preview)")
    print("\n📋 Available test models:")
    print("- gemini_25_flash_test (for general AI tasks)")
    print("- gemini_25_pro_test (for advanced reasoning)")
    print("\n🚀 Usage examples:")
    print("-- Use Flash model for general tasks:")
    print("SELECT response FROM gemini_25_flash_test WHERE user_input = 'Your question here';")
    print("\n-- Use Pro model for complex analysis:")
    print("SELECT analysis FROM gemini_25_pro_test WHERE complex_query = 'Your complex query here';")
    
    return True

if __name__ == "__main__":
    main()
