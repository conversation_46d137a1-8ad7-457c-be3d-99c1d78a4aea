#!/usr/bin/env python3
"""
Execute Respond Interface Setup Commands
========================================

This script executes SQL commands to set up the Respond chat interface
in MindsDB via the HTTP API.
"""

import requests
import json
import time
import sys

# MindsDB Configuration
MINDSDB_BASE_URL = "http://localhost:47334"
MINDSDB_SQL_URL = f"{MINDSDB_BASE_URL}/api/sql/query"

def execute_sql(query: str, description: str = "") -> bool:
    """Execute SQL query against MindsDB"""
    try:
        print(f"🔧 {description}")
        print(f"📝 Query: {query[:100]}...")

        response = requests.post(
            MINDSDB_SQL_URL,
            json={"query": query},
            headers={"Content-Type": "application/json"},
            auth=("mindsdb", "mindsdb123"),  # Add basic authentication
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success: {description}")
            if "data" in result and result["data"]:
                print(f"📊 Result: {json.dumps(result['data'], indent=2)}")
            return True
        else:
            print(f"❌ Failed: {description}")
            print(f"📄 Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception in {description}: {str(e)}")
        return False

def setup_respond_interface():
    """Set up the Respond chat interface"""
    print("🚀 Setting up MindsDB Respond Chat Interface")
    print("=" * 60)
    
    # List of SQL commands to execute
    commands = [
        {
            "query": "SHOW DATABASES;",
            "description": "Checking available databases"
        },
        {
            "query": "SHOW ML_ENGINES;",
            "description": "Checking available ML engines"
        },
        {
            "query": """
                CREATE ML_ENGINE IF NOT EXISTS openai_engine
                FROM openai
                USING
                    api_key = 'your-openai-key-here';
            """,
            "description": "Creating OpenAI ML engine (will fail without API key, but that's OK)"
        },
        {
            "query": """
                CREATE MODEL IF NOT EXISTS simple_chat_model
                PREDICT response
                USING
                    prompt_template = 'You are a helpful AI assistant for MindsDB. Answer the following question: {{question}}';
            """,
            "description": "Creating simple chat model"
        },
        {
            "query": """
                CREATE AGENT IF NOT EXISTS respond_chat_agent
                USING
                    model = 'simple_chat_model',
                    prompt_template = 'You are an intelligent data assistant powered by MindsDB. You can help users query their data using natural language. Answer: {{question}}';
            """,
            "description": "Creating Respond chat agent"
        },
        {
            "query": "SHOW MODELS;",
            "description": "Listing created models"
        },
        {
            "query": "SHOW AGENTS;",
            "description": "Listing created agents"
        }
    ]
    
    # Execute commands
    success_count = 0
    for i, cmd in enumerate(commands, 1):
        print(f"\n📋 Step {i}/{len(commands)}: {cmd['description']}")
        if execute_sql(cmd["query"], cmd["description"]):
            success_count += 1
        time.sleep(2)  # Small delay between commands
    
    print(f"\n📊 Setup Results: {success_count}/{len(commands)} commands successful")
    
    # Test the chat model if it was created
    if success_count >= 4:  # If at least the model creation succeeded
        print("\n🧪 Testing chat functionality...")
        test_query = """
            SELECT response 
            FROM simple_chat_model 
            WHERE question = 'Hello! What is MindsDB?' 
            LIMIT 1;
        """
        
        if execute_sql(test_query, "Testing chat model"):
            print("✅ Chat model is working!")
        else:
            print("⚠️  Chat model test failed, but setup may still be successful")
    
    return success_count >= len(commands) // 2  # Success if at least half the commands worked

def check_respond_interface():
    """Check if Respond interface should be available"""
    print("\n🔍 Checking Respond interface availability...")
    
    # Check if MCP API is responding
    try:
        mcp_response = requests.get(
            "http://localhost:47337/sse",
            headers={"Authorization": "Bearer mcp_token_abc123"},
            timeout=5
        )
        
        if mcp_response.status_code == 200:
            print("✅ MCP API is responding")
            mcp_ok = True
        else:
            print(f"⚠️  MCP API status: {mcp_response.status_code}")
            mcp_ok = False
    except Exception as e:
        print(f"❌ MCP API error: {str(e)}")
        mcp_ok = False
    
    # Check web interface
    try:
        web_response = requests.get("http://localhost:47334/", auth=("mindsdb", "mindsdb123"), timeout=5)
        if web_response.status_code == 200:
            print("✅ Web interface is accessible with auth")
            web_ok = True
        else:
            print(f"⚠️  Web interface status: {web_response.status_code}")
            web_ok = False
    except Exception as e:
        print(f"❌ Web interface error: {str(e)}")
        web_ok = False
    
    return mcp_ok, web_ok

def print_final_instructions(setup_success: bool, mcp_ok: bool, web_ok: bool):
    """Print final instructions for accessing the Respond interface"""
    print("\n" + "=" * 60)
    print("🎉 Respond Interface Setup Complete!")
    print("=" * 60)
    
    print(f"\n📊 **Setup Status:**")
    print(f"- SQL Setup: {'✅' if setup_success else '❌'}")
    print(f"- MCP API: {'✅' if mcp_ok else '⚠️'}")
    print(f"- Web Interface: {'✅' if web_ok else '❌'}")
    
    print(f"\n🌐 **Access the Respond Chat Interface:**")
    print(f"1. Open: http://localhost:47334/")
    print(f"2. Look for: Robot icon (🤖), 'Respond', 'Chat', or 'AI Assistant' section")
    print(f"3. If you see a login screen, use: mindsdb / mindsdb123")
    
    if setup_success and web_ok:
        print(f"\n✅ **Expected Behavior:**")
        print(f"- The web interface should now show the Respond section")
        print(f"- You should be able to ask questions in natural language")
        print(f"- The chat agent should respond to your queries")
    else:
        print(f"\n🔧 **Troubleshooting:**")
        print(f"- Check container logs: docker logs mindsdb_chat")
        print(f"- Restart container: docker restart mindsdb_chat")
        print(f"- Try refreshing the web page")
    
    print(f"\n🛠️  **Manual Testing:**")
    print(f"If the Respond interface isn't visible, test the chat model directly:")
    print(f"curl -X POST http://localhost:47334/api/sql/query \\")
    print(f"  -H 'Content-Type: application/json' \\")
    print(f"  -d '{{\"query\": \"SELECT response FROM simple_chat_model WHERE question = \\'Hello!\\' LIMIT 1;\"}}'")
    
    print(f"\n📚 **Additional Resources:**")
    print(f"- MindsDB Documentation: https://docs.mindsdb.com/")
    print(f"- MCP Documentation: https://docs.mindsdb.com/mcp/overview")
    print(f"- Respond Interface: https://docs.mindsdb.com/mindsdb-respond")

def main():
    """Main execution"""
    # Set up the Respond interface
    setup_success = setup_respond_interface()
    
    # Check interface availability
    mcp_ok, web_ok = check_respond_interface()
    
    # Print final instructions
    print_final_instructions(setup_success, mcp_ok, web_ok)
    
    # Open browser if everything looks good
    if setup_success and web_ok:
        print(f"\n🌐 Opening web interface...")
        import webbrowser
        try:
            webbrowser.open("http://localhost:47334/")
            print(f"✅ Browser opened to MindsDB interface")
        except:
            print(f"⚠️  Could not open browser automatically")
    
    return setup_success and web_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
